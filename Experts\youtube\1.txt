Youtue: https://www.youtube.com/watch?v=2CUvhvcjo1w

请根据图中的策略,帮我写一个mt4平台的MQL4语言的EA,用于交易测试:

下面是根据图中“剃头皮策略（Scalping）”要求写的一个 MQL4 EA（完整代码）。实现要点与映射说明：

* 进场条件：使用 RSI 指标

  * RSI < Oversold（默认30）时开多单
  * RSI > Overbought（默认70）时开空单
* 仓位：可以直接指定手数（Lot），也可启用按风险自动计算手数（AutoLot，根据账户余额 & 风险百分比与止损距离估算）
* 止损（SL）：按百分比（如 0.5%~1%）计算，挂在成交价下方/上方（示例用百分比）
* 止盈（TP）：分批：第一批在 TP1（默认1%）锁定，第二批在 TP2（默认3%）平仓

  * 实现方式：一次下两笔市价单（两个相等手数或按设置分配），分别设置不同的 TP
* 跟踪止损：可选（UseTrailing），当收益达到 TrailingStartPercent 时开始移动止损，按 TrailingStepPercent 更新
* 其他风控：最大允许点差（MaxSpread），下单滑点（Slippage），MagicNumber 区分 EA 订单
* 适用于高流动性合约（BTC/ETH 等），推荐在 5~15 分钟 K 线上运行

把下面代码复制到 MetaEditor，保存为 `ScalpByRSI.mq4`，编译后挂到图表上（建议 M5 或 M15）测试。

---

```mql4
//+------------------------------------------------------------------+
//|                                                      ScalpByRSI |
//|  基于图中剃头皮策略的示例 EA                                     |
//|  作者: ChatGPT (示例代码)                                         |
//+------------------------------------------------------------------+
#property copyright "ScalpByRSI"
#property link      ""
#property version   "1.00"
#property strict

//----- inputs (可调参数) -----
input int    RSI_Period           = 14;      // RSI 周期
input int    RSI_Oversold         = 30;      // RSI 超卖阈值 -> 做多
input int    RSI_Overbought       = 70;      // RSI 超买阈值 -> 做空

input double Lot                  = 0.01;    // 固定手数（若 AutoLot=false 则使用）
input bool   AutoLot              = false;   // 是否根据风险自动计算手数
input double RiskPercent          = 0.5;     // 自动手数时风险 %（账户余额的百分比）例如 0.5 = 0.5%

input double SL_Percent           = 0.8;     // 止损百分比（相对开仓价），例如 0.8 表示 0.8%
input double TP1_Percent          = 1.0;     // 第一批止盈百分比（锁定一半仓位）
input double TP2_Percent          = 3.0;     // 第二批止盈百分比（平掉剩余仓位）

input double SplitRatio1         = 0.5;      // 第一批占总仓位比例（0~1），默认 50%
input int    MagicNumber         = 20251002; // Magic number
input int    Slippage            = 20;       // 最大滑点（点）
input double MaxSpread           = 50;       // 最大允许点差（点），超过不下单

input bool   UseTrailing         = true;     // 是否启用跟踪止损
input double TrailingStartPercent= 0.8;      // 当收益达到该百分比（相对开仓价）开始跟踪
input double TrailingStepPercent = 0.5;      // 每次将止损抬到当前价减该百分比

//----- 全局变量 -----
datetime lastNewBarTime = 0;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
  {
   lastNewBarTime = 0;
   return(INIT_SUCCEEDED);
  }

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
  {
  }

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
  {
   // 不在交易时判断
   if(IsTradingAllowed()==false) return;

   // 控制点差
   double spread = (Ask - Bid) / Point;
   if(spread > MaxSpread) {
      // 点差过大，不交易
   }

   // 检测新 K 线（以当前图表周期）
   datetime currentBarTime = iTime(Symbol(),Period(),0);
   if(currentBarTime != lastNewBarTime)
     {
      // 新棒
      lastNewBarTime = currentBarTime;
      OnNewBar();
     }

   // 每个 Tick 进行跟踪止损管理
   ManageTrailingStops();
  }

//+------------------------------------------------------------------+
//| 新 K 线处理（决策在这里）                                        |
//+------------------------------------------------------------------+
void OnNewBar()
  {
   // 检查点差限制
   double spread = (Ask - Bid) / Point;
   if(spread > MaxSpread) return;

   // 读取 RSI 值（当前 K 线刚生成时取上一个收盘的 RSI）
   double rsi = iRSI(Symbol(),Period(),RSI_Period,PRICE_CLOSE,1); // 使用上一个已完成的吧（索引1）
   // 记录日志（可选）
   // Print("RSI=",DoubleToStr(rsi,2));

   // 是否已有相同方向持仓（由 MagicNumber 区分）
   bool haveLong = HasOpenOrders(OP_BUY);
   bool haveShort = HasOpenOrders(OP_SELL);

   // 若 RSI 超卖 -> 建多
   if(rsi < RSI_Oversold && !haveLong)
     {
      OpenScalpPosition(OP_BUY);
     }
   // 若 RSI 超买 -> 建空
   if(rsi > RSI_Overbought && !haveShort)
     {
      OpenScalpPosition(OP_SELL);
     }
  }

//+------------------------------------------------------------------+
//| 打开剃头皮仓位：分两笔下单，分别设置不同 TP（TP1, TP2）         |
//+------------------------------------------------------------------+
void OpenScalpPosition(int cmd)
  {
   // 计算手数
   double totalLot = Lot;
   if(AutoLot)
     {
      totalLot = CalculateLotByRisk(cmd);
      if(totalLot <= 0) return;
     }

   // 按比例拆分
   double lot1 = NormalizeLot(totalLot * SplitRatio1);
   double lot2 = NormalizeLot(totalLot - lot1);

   if(lot1 < MarketInfo(Symbol(),MODE_MINLOT) && lot2 < MarketInfo(Symbol(),MODE_MINLOT))
     {
      Print("手数过小，无法下单: lot1=",DoubleToStr(lot1,2)," lot2=",DoubleToStr(lot2,2));
      return;
     }

   double price = (cmd==OP_BUY) ? Ask : Bid;
   double sl_price, tp1_price, tp2_price;

   if(cmd==OP_BUY)
     {
      sl_price  = price * (1.0 - SL_Percent/100.0);
      tp1_price = price * (1.0 + TP1_Percent/100.0);
      tp2_price = price * (1.0 + TP2_Percent/100.0);
     }
   else
     {
      sl_price  = price * (1.0 + SL_Percent/100.0);
      tp1_price = price * (1.0 - TP1_Percent/100.0);
      tp2_price = price * (1.0 - TP2_Percent/100.0);
     }

   // 确保价格符合最小最小阶梯
   sl_price  = NormalizePrice(sl_price);
   tp1_price = NormalizePrice(tp1_price);
   tp2_price = NormalizePrice(tp2_price);

   int ticket1 = -1, ticket2 = -1;

   // 第一笔
   if(lot1 >= MarketInfo(Symbol(),MODE_MINLOT))
     {
      ticket1 = OrderSend(Symbol(),cmd,lot1, price, Slippage, sl_price, tp1_price, "Scalp TP1", MagicNumber,0,clrGreen);
      if(ticket1>0)
         Print("Opened scalp order1: ticket=",ticket1," type=",cmd," lot=",DoubleToStr(lot1,2));
      else
         Print("OrderSend1 failed: ",GetLastError());
     }

   // 第二笔
   if(lot2 >= MarketInfo(Symbol(),MODE_MINLOT))
     {
      ticket2 = OrderSend(Symbol(),cmd,lot2, price, Slippage, sl_price, tp2_price, "Scalp TP2", MagicNumber,0,clrBlue);
      if(ticket2>0)
         Print("Opened scalp order2: ticket=",ticket2," type=",cmd," lot=",DoubleToStr(lot2,2));
      else
         Print("OrderSend2 failed: ",GetLastError());
     }
  }

//+------------------------------------------------------------------+
//| 计算按风险自动手数（简单估算：RiskPercent% * Balance / (stop_loss_in_account_currency)) |
//| 说明：对于不同品种/经纪商，准确计算需要用点值 (MarketInfo MODE_TICKVALUE等)，此处做常见估算   |
//+------------------------------------------------------------------+
double CalculateLotByRisk(int cmd)
  {
   double balance = AccountBalance();
   double riskMoney = balance * (RiskPercent / 100.0);

   double price = (cmd==OP_BUY) ? Ask : Bid;
   double sl_price;
   if(cmd==OP_BUY) sl_price = price * (1.0 - SL_Percent/100.0);
   else sl_price = price * (1.0 + SL_Percent/100.0);

   double stopDistance = MathAbs(price - sl_price); // 以价格计
   if(stopDistance <= 0) return(0);

   // 获取每手1.0的点值（估算）
   double tickValue = MarketInfo(Symbol(), MODE_TICKVALUE);
   double tickSize  = MarketInfo(Symbol(), MODE_TICKSIZE);
   if(tickValue<=0 || tickSize<=0) {
      // 无法准确计算，返回默认手数
      return(Lot);
   }
   // 将 price 距离转换为点数
   double points = stopDistance / tickSize;
   double valuePerLot = tickValue * points; // 1手止损时的金额
   if(valuePerLot <= 0) return(0);

   double lot = riskMoney / valuePerLot;
   // 按最小变动和最大限制调整
   double minLot = MarketInfo(Symbol(), MODE_MINLOT);
   double maxLot = MarketInfo(Symbol(), MODE_MAXLOT);
   double stepLot = MarketInfo(Symbol(), MODE_LOTSTEP);
   if(lot < minLot) lot = 0;
   if(lot > maxLot) lot = maxLot;
   // 标准化
   lot = MathFloor(lot / stepLot) * stepLot;
   return(NormalizeLot(lot));
  }

//+------------------------------------------------------------------+
//| 跟踪止损管理（把盈利达到阈值的订单的止损挪到更安全位置）            |
//+------------------------------------------------------------------+
void ManageTrailingStops()
  {
   if(!UseTrailing) return;

   for(int i=OrdersTotal()-1;i>=0;i--)
     {
      if(OrderSelect(i,SELECT_BY_POS,MODE_TRADES))
        {
         if(OrderMagicNumber()!=MagicNumber || OrderSymbol()!=Symbol()) continue;

         int type = OrderType();
         double openPrice = OrderOpenPrice();
         double currentPrice = (type==OP_BUY) ? Bid : Ask;
         double profitPct = ((type==OP_BUY) ? (currentPrice - openPrice) : (openPrice - currentPrice)) / openPrice * 100.0;

         if(profitPct >= TrailingStartPercent)
           {
            // 计算新的止损价：把止损移动到比当前价低（多单）或高（空单） TrailingStepPercent
            double newSL;
            if(type==OP_BUY)
               newSL = currentPrice * (1.0 - TrailingStepPercent/100.0);
            else
               newSL = currentPrice * (1.0 + TrailingStepPercent/100.0);

            newSL = NormalizePrice(newSL);

            // 只在更好的情况下修改止损（即多单时新SL高于旧SL；空单时新SL低于旧SL）
            if((type==OP_BUY && newSL > OrderStopLoss()) || (type==OP_SELL && newSL < OrderStopLoss()))
              {
               bool mod = OrderModify(OrderTicket(), OrderOpenPrice(), newSL, OrderTakeProfit(), 0, clrYellow);
               if(mod)
                  Print("Trailing SL modified ticket=",OrderTicket()," newSL=",DoubleToStr(newSL,Digits));
               else
                  Print("Trailing modify failed ticket=",OrderTicket()," err=",GetLastError());
              }
           }
        }
     }
  }

//+------------------------------------------------------------------+
//| 是否存在该方向的订单（由 MagicNumber 和 Symbol 限定）             |
//+------------------------------------------------------------------+
bool HasOpenOrders(int type)
  {
   for(int i=OrdersTotal()-1;i>=0;i--)
     {
      if(OrderSelect(i,SELECT_BY_POS,MODE_TRADES))
        {
         if(OrderSymbol()==Symbol() && OrderMagicNumber()==MagicNumber && OrderType()==type)
            return true;
        }
     }
   return false;
  }

//+------------------------------------------------------------------+
//| 将价格标准化到 broker 的小数位                                    |
//+------------------------------------------------------------------+
double NormalizePrice(double price)
  {
   return(NormalizeDouble(price,Digits));
  }

//+------------------------------------------------------------------+
//| 将手数修正到合约步进并规范化                                     |
//+------------------------------------------------------------------+
double NormalizeLot(double lot)
  {
   double step = MarketInfo(Symbol(),MODE_LOTSTEP);
   double minl = MarketInfo(Symbol(),MODE_MINLOT);
   double maxl = MarketInfo(Symbol(),MODE_MAXLOT);
   if(lot < minl) return(0);
   if(lot > maxl) lot = maxl;
   // 按步进向下取整
   double n = MathFloor(lot / step);
   double r = n * step;
   // 避免负数
   if(r < minl) r = minl;
   return(r);
  }

//+------------------------------------------------------------------+
```

---

### 使用建议与注意事项（重要）

1. **回测前请仔细检查合约最小手数、步长和点值**（`MarketInfo(...,MODE_MINLOT/MODE_LOTSTEP/MODE_TICKVALUE)`），尤其加密对的报价与 Forex 不同。若数据不正确，自动算手数（AutoLot）可能失效，请先用固定 `Lot` 测试。
2. **图中策略提醒了“低杠杆(3-5x)”**——MT4 本身没有杠杆设置（由经纪商/合约决定），本 EA 没处理杠杆，请在账户或经纪参数中控制杠杆或合约乘数。
3. **止损以百分比计算**，适合现货/合约按价格的简单实现；若你的品种使用不同报价单位（例如合约点值、USD/合约），需要根据经纪商调整 `CalculateLotByRisk` 中的点值换算。
4. **分批实现**：EA 使用两笔单子分别设置 TP1/TP2 来实现“先锁定一半1%，剩余3%”。若经纪商不允许同时多单，请把 `SplitRatio1` 设置为 1 并调整 TP2 或手动管理。
5. **跟踪止损逻辑**为简单示例：当收益 >= TrailingStartPercent 时，把止损抬到当前价减去 TrailingStepPercent；可按需要改成基于点数或波动率的更复杂逻辑。
6. 在真实资金前，请**充分回测与策略优化（不同时间框架、不同点差、滑点模拟）**。

---

如果你愿意，我可以进一步：

* 根据你提供的具体交易品种（例如 `BTCUSD` 在你经纪商的符号名）调整 `MarketInfo` 处理和默认参数；
* 把止损/止盈从“百分比”改用“最近摆动低点/高点”（图中推荐“置于最近摆动低点下方”）——这需要把逻辑改为读取近期 High/Low；
* 改用布林带（Bollinger Bands）替代或与 RSI 结合做双指标过滤；
* 增加日志/可视化（画线标注开仓/止损/止盈）和回测优化参数。

你要我现在直接把 EA 改成“使用布林带 + RSI 双重过滤”或“止损基于最近摆动低点”吗？（如果有目标品种名也请直接告诉我，我会据此微调代码并贴出新版。）
好 👍
既然你指定了品种 **SOL/USD**，我帮你把 EA 改成更贴合图中策略的版本：

1. **进场条件（双指标过滤）：**

   * **RSI**：<30 买入，>70 卖出；
   * **布林带**：价格触及布林带下轨配合 RSI<30 买入；触及上轨配合 RSI>70 卖出；
   * 两个条件同时满足才开仓。

2. **止损逻辑：**

   * 不再用百分比，而是**放在最近的摆动低点/高点（swing low/high）**；
   * 摆动点用最近 N 根 K 线的低点/高点（输入参数 SwingBars）。

3. **止盈逻辑：**

   * 分两批：第一批 TP=+1%（锁定一半仓位），第二批 TP=+3%；
   * 或者（可选参数）使用布林带中轨作为 TP（动态出场）。

4. **品种：**

   * 默认挂在 `SOLUSD` 图表上；
   * AutoLot 模式也会适配（但注意 SOL 合约的点值由经纪商决定，请在回测/模拟盘确认）。

下面是改进后的 **MQL4 EA 完整代码**，保存为 `Scalp_SOLUSD.mq4` 即可：

```mql4
//+------------------------------------------------------------------+
//|                                                  Scalp_SOLUSD.mq4|
//|  剃头皮策略 EA - SOL/USD 专用                                    |
//+------------------------------------------------------------------+
#property strict

//--- 输入参数
input int    RSI_Period       = 14;
input int    RSI_Oversold     = 30;
input int    RSI_Overbought   = 70;

input int    BB_Period        = 20;
input double BB_Dev           = 2.0;

input int    SwingBars        = 5;        // 最近多少根K线作为摆动点

input double Lot              = 0.1;
input bool   AutoLot          = false;
input double RiskPercent      = 1.0;      // AutoLot风险百分比

input double TP1_Percent      = 1.0;      // 第一批止盈
input double TP2_Percent      = 3.0;      // 第二批止盈
input double SplitRatio1      = 0.5;

input bool   UseBB_MidAsTP    = false;    // 是否用布林带中轨做TP

input int    MagicNumber      = 20251002;
input int    Slippage         = 20;
input double MaxSpread        = 100;

//--- 全局
datetime lastBar = 0;

//+------------------------------------------------------------------+
//| 初始化                                                          |
//+------------------------------------------------------------------+
int OnInit()
  {
   return(INIT_SUCCEEDED);
  }

//+------------------------------------------------------------------+
//| Tick 主循环                                                     |
//+------------------------------------------------------------------+
void OnTick()
  {
   // 新K线时触发策略
   datetime t = iTime(Symbol(),Period(),0);
   if(t != lastBar)
     {
      lastBar = t;
      CheckEntry();
     }
  }

//+------------------------------------------------------------------+
//| 检查进场                                                        |
//+------------------------------------------------------------------+
void CheckEntry()
  {
   // 控制点差
   double spread = (Ask - Bid) / Point;
   if(spread > MaxSpread) return;

   // 指标
   double rsi = iRSI(Symbol(),Period(),RSI_Period,PRICE_CLOSE,1);
   double bb_mid = iBands(Symbol(),Period(),BB_Period,BB_Dev,0,PRICE_CLOSE,MODE_MAIN,1);
   double bb_up  = iBands(Symbol(),Period(),BB_Period,BB_Dev,0,PRICE_CLOSE,MODE_UPPER,1);
   double bb_low = iBands(Symbol(),Period(),BB_Period,BB_Dev,0,PRICE_CLOSE,MODE_LOWER,1);

   double close1 = iClose(Symbol(),Period(),1);

   bool haveLong  = HasOpenOrders(OP_BUY);
   bool haveShort = HasOpenOrders(OP_SELL);

   // 多头信号：RSI<30 + 收盘价<=下轨
   if(rsi < RSI_Oversold && close1 <= bb_low && !haveLong)
      OpenTrade(OP_BUY, bb_mid);

   // 空头信号：RSI>70 + 收盘价>=上轨
   if(rsi > RSI_Overbought && close1 >= bb_up && !haveShort)
      OpenTrade(OP_SELL, bb_mid);
  }

//+------------------------------------------------------------------+
//| 开仓函数                                                        |
//+------------------------------------------------------------------+
void OpenTrade(int cmd, double bb_mid)
  {
   double lot = Lot;
   if(AutoLot) lot = CalcAutoLot(cmd);
   if(lot <= 0) return;

   double lot1 = NormalizeLot(lot * SplitRatio1);
   double lot2 = NormalizeLot(lot - lot1);

   double price = (cmd==OP_BUY) ? Ask : Bid;
   double sl    = FindSwingSL(cmd);

   double tp1,tp2;
   if(UseBB_MidAsTP)
     {
      tp1 = bb_mid;
      tp2 = bb_mid;
     }
   else
     {
      if(cmd==OP_BUY)
        {
         tp1 = price * (1.0 + TP1_Percent/100.0);
         tp2 = price * (1.0 + TP2_Percent/100.0);
        }
      else
        {
         tp1 = price * (1.0 - TP1_Percent/100.0);
         tp2 = price * (1.0 - TP2_Percent/100.0);
        }
     }

   // 第一批
   if(lot1 >= MarketInfo(Symbol(),MODE_MINLOT))
      OrderSend(Symbol(),cmd,lot1,price,Slippage,sl,tp1,"ScalpTP1",MagicNumber,0,clrGreen);

   // 第二批
   if(lot2 >= MarketInfo(Symbol(),MODE_MINLOT))
      OrderSend(Symbol(),cmd,lot2,price,Slippage,sl,tp2,"ScalpTP2",MagicNumber,0,clrBlue);
  }

//+------------------------------------------------------------------+
//| 摆动点止损                                                      |
//+------------------------------------------------------------------+
double FindSwingSL(int cmd)
  {
   double sl = 0;
   if(cmd==OP_BUY)
     {
      double minLow = iLow(Symbol(),Period(),1);
      for(int i=2;i<=SwingBars;i++)
         minLow = MathMin(minLow,iLow(Symbol(),Period(),i));
      sl = minLow;
     }
   else
     {
      double maxHigh = iHigh(Symbol(),Period(),1);
      for(int i=2;i<=SwingBars;i++)
         maxHigh = MathMax(maxHigh,iHigh(Symbol(),Period(),i));
      sl = maxHigh;
     }
   return NormalizeDouble(sl,Digits);
  }

//+------------------------------------------------------------------+
//| 自动手数计算（简化）                                            |
//+------------------------------------------------------------------+
double CalcAutoLot(int cmd)
  {
   double balance = AccountBalance();
   double riskMoney = balance * RiskPercent / 100.0;

   double price = (cmd==OP_BUY)?Ask:Bid;
   double sl = FindSwingSL(cmd);
   double dist = MathAbs(price - sl);

   double tickValue = MarketInfo(Symbol(),MODE_TICKVALUE);
   double tickSize  = MarketInfo(Symbol(),MODE_TICKSIZE);
   if(tickValue<=0 || tickSize<=0 || dist<=0) return Lot;

   double points = dist / tickSize;
   double valuePerLot = tickValue * points;
   if(valuePerLot <= 0) return Lot;

   double lot = riskMoney / valuePerLot;
   return NormalizeLot(lot);
  }

//+------------------------------------------------------------------+
//| 工具函数                                                        |
//+------------------------------------------------------------------+
bool HasOpenOrders(int type)
  {
   for(int i=OrdersTotal()-1;i>=0;i--)
      if(OrderSelect(i,SELECT_BY_POS,MODE_TRADES))
         if(OrderSymbol()==Symbol() && OrderMagicNumber()==MagicNumber && OrderType()==type)
            return true;
   return false;
  }

double NormalizeLot(double lot)
  {
   double step = MarketInfo(Symbol(),MODE_LOTSTEP);
   double minl = MarketInfo(Symbol(),MODE_MINLOT);
   double maxl = MarketInfo(Symbol(),MODE_MAXLOT);
   if(lot < minl) return 0;
   if(lot > maxl) lot = maxl;
   double n = MathFloor(lot/step);
   return n*step;
  }
```

---

### 使用说明

1. 挂到 **SOL/USD** 的 M5 或 M15 图表上。
2. 默认逻辑：

   * RSI<30 且触碰布林带下轨 → 买入；
   * RSI>70 且触碰布林带上轨 → 卖出；
   * 止损在最近 SwingBars（默认5根）K 线的最低点或最高点；
   * TP 分两批：+1% / +3% 或布林带中轨（UseBB_MidAsTP=true）。
3. 真实交易前请先**回测和模拟盘验证**，特别是 SOLUSD 在不同经纪商的报价精度、点值、合约规则。

---

要不要我帮你**在 EA 图表上画出进场/止损/止盈的箭头和线条**，方便回测可视化？
太好了 👍
那我们就用 **SOL/USD** 作为品种来练习。

接下来建议分三步：

### **第一步：数据准备**

* 需要收集一段时间的 SOL/USD 历史数据（最好是日线 K 线，包含 `开盘价、最高价、最低价、收盘价、成交量`）。
* 数据来源可以是 **Binance API、Coinbase API** 或者 **TradingView 导出**。
* 保存成 CSV 格式，方便后面做指标计算。

### **第二步：指标计算**

基于你之前说的思路，可以先做以下指标：

* **MACD**（趋势判断）
* **KDJ**（超买超卖）
* **5日 / 10日 / 30日均线**（趋势强弱）
* **RSI**（动能强弱）

这些指标都可以通过 **pandas + ta-lib**（或 `pandas_ta`）来计算。

### **第三步：模型预测目标**

目标定义：

* 预测未来 **3天内价格是否上涨超过 5%**。
* 即构造一个二分类任务：

  * **1** → 未来3天涨幅 > 5%
  * **0** → 否则

这样就可以用 **机器学习模型（XGBoost、LSTM、Transformer）** 来预测。

---

要不要我先帮你写一个 **Python 脚本**，直接从 Binance 拉取 **SOL/USDT 日线数据**，存成 CSV，然后顺便计算好 MACD、KDJ、均线和 RSI？
