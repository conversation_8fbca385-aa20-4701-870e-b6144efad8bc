//+------------------------------------------------------------------+
//| Full EA: State-machine + H4 Stochastic trend + M5 MA signals     |
//| Smart Trailing (High/Low + MA) + SL/TP + per-order retry        |
//| Language: MQL4                                                   |
//| Note: 回测与模拟充分测试后再实盘                                   |
//+------------------------------------------------------------------+
#property strict

//---- INPUTS
input double Lots = 0.1;                   // 每次交易手数
input double StopLossPips = 30;            // 初始固定止损（pips）
input double TakeProfitPips = 60;          // 初始固定止盈（pips）
input int    Slippage = 3;                 // 允许滑点
input int    TrailingLookbackBars = 5;     // 追踪止损计算K线数
input double TrailingBufferPips = 5;       // 高低点与MA止损缓冲（pips）
input double TrailingActivatePips = 20;    // 浮盈达到多少点开始追踪（pips）
input int    MagicNumber = 123456;         // 订单魔术号

// Stochastic (H4)
input int Stoch_Kperiod = 20;
input int Stoch_Dperiod = 2;
input int Stoch_Slowing = 2;
input int Stoch_ma_method = MODE_SMA;
input int Stoch_price_field = 0; // PRICE_CLOSE

// MA periods (M5)
input int MA6_period = 6;
input int MA2_period = 2;

// Retry parameters
input int MaxSendRetries = 3;
input int MaxCloseRetries = 3;
input int RetryDelayMs = 500;

// Add limits
input int MaxAdds = 2;                 // 每方向最大加仓次数

// Time threshold for 4 hours (seconds)
#define MIN_HOLD_SECONDS  (4*3600)

//---- GLOBALS
int trendState = 0; // 0-8 状态机
double point;
datetime lastEnteredBuyable = 0;
datetime lastEnteredSellable = 0;
int addsCountBuy = 0;
int addsCountSell = 0;

//+------------------------------------------------------------------+
//| Initialization                                                   |
//+------------------------------------------------------------------+
int OnInit()
{
  point = MarketInfo(Symbol(), MODE_POINT);
  Print("EA initialized.");
  return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Deinitialization                                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
  Print("EA deinitialized, reason=", reason);
}

//+------------------------------------------------------------------+
//| Main Tick                                                         |
//+------------------------------------------------------------------+
void OnTick()
{
  if(Bars < 100) return; // 简单保护
  UpdateTrendState();
  CheckTradingSignals();
  SmartTrailingStops();
  static int tickCounter = 0;
  tickCounter++;
  if(tickCounter % 300 == 0){
    PrintFormat("State=%d BuyLots=%.2f SellLots=%.2f AddsBuy=%d AddsSell=%d",
                trendState, TotalLotsBuy(), TotalLotsSell(), addsCountBuy, addsCountSell);
  }
}

//+------------------------------------------------------------------+
//| --- Indicator Helpers ---                                         |
//+------------------------------------------------------------------+
double StochMain(int shift){
  return iStochastic(NULL, PERIOD_H4, Stoch_Kperiod, Stoch_Dperiod, Stoch_Slowing, Stoch_ma_method, Stoch_price_field, MODE_MAIN, shift);
}
double StochSignal(int shift){
  return iStochastic(NULL, PERIOD_H4, Stoch_Kperiod, Stoch_Dperiod, Stoch_Slowing, Stoch_ma_method, Stoch_price_field, MODE_SIGNAL, shift);
}

double MA_value(int period, int ma_method, int applied_price, int shift){
  return iMA(NULL, PERIOD_M5, period, 0, ma_method, applied_price, shift);
}
double MA_a(int shift){ return MA_value(MA6_period, MODE_SMA, PRICE_HIGH, shift); }
double MA_b(int shift){ return MA_value(MA6_period, MODE_SMA, PRICE_LOW, shift); }
double MA_c(int shift){ return MA_value(MA6_period, MODE_SMA, PRICE_MEDIAN, shift); }
double MA_d(int shift){ return MA_value(MA2_period, MODE_SMA, PRICE_HIGH, shift); }
double MA_e(int shift){ return MA_value(MA2_period, MODE_SMA, PRICE_LOW, shift); }
double MA_f(int shift){ return MA_value(MA2_period, MODE_SMA, PRICE_MEDIAN, shift); }

//+------------------------------------------------------------------+
//| --- Position helpers ---                                          |
//+------------------------------------------------------------------+
double TotalLotsBuy(){
  double s = 0.0;
  for(int i=0;i<OrdersTotal();i++){
    if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES)){
      if(OrderMagicNumber()==MagicNumber && OrderSymbol()==Symbol() && OrderType()==OP_BUY) s += OrderLots();
    }
  }
  return s;
}
double TotalLotsSell(){
  double s = 0.0;
  for(int i=0;i<OrdersTotal();i++){
    if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES)){
      if(OrderMagicNumber()==MagicNumber && OrderSymbol()==Symbol() && OrderType()==OP_SELL) s += OrderLots();
    }
  }
  return s;
}
int OrdersTotalByMagic(){
  int cnt=0;
  for(int i=0;i<OrdersTotal();i++){
    if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES)){
      if(OrderMagicNumber()==MagicNumber && OrderSymbol()==Symbol()) cnt++;
    }
  }
  return cnt;
}

//+------------------------------------------------------------------+
//| UpdateTrendState: 完整 0-8 状态机实现 (基于 H4 Stochastic)         |
//+------------------------------------------------------------------+
void UpdateTrendState()
{
  double main0 = StochMain(0);
  double sig0  = StochSignal(0);
  double main1 = StochMain(1);
  double sig1  = StochSignal(1);

  datetime now = TimeCurrent();

  bool bar0_up_cross = (main0 > sig0) && (main1 <= sig1);    // Bar(0) main 上穿 signal 且 Bar(1) main <= signal
  bool bar0_down_cross = (main0 < sig0) && (main1 >= sig1);  // Bar(0) main 下穿 signal 且 Bar(1) main >= signal

  double totalLots = TotalLotsBuy() + TotalLotsSell();

  // 空仓情况
  if(totalLots < 0.000001){
    // 空仓可买 (1)
    if(bar0_up_cross && sig0 <= 30.0){
      if(trendState != 1){
        trendState = 1;
        lastEnteredBuyable = now;
        Print("State -> EMPTY_CAN_BUY (1)");
      }
      return;
    }
    // 空仓可卖 (2)
    if(bar0_down_cross && sig0 >= 70.0){
      if(trendState != 2){
        trendState = 2;
        lastEnteredSellable = now;
        Print("State -> EMPTY_CAN_SELL (2)");
      }
      return;
    }
    // 其余保持空仓等待 (0)
    if(trendState != 0){
      trendState = 0;
      Print("State -> WAIT_EMPTY (0)");
    }
    return;
  }

  // 如果持有多头
  double buyLots = TotalLotsBuy();
  if(buyLots > 0.0){
    // 持买仓可平仓 (7): 持仓 >=4小时 && Bar(0) main 向下穿 signal && signal >=70 && Bar(1) main >= signal
    if( (now - lastEnteredBuyable) >= MIN_HOLD_SECONDS && bar0_down_cross && sig0 >= 70.0 && (main1 >= sig1) ){
      if(trendState != 7){
        trendState = 7;
        Print("State -> HOLD_BUY_CLOSEABLE (7)");
      }
      return;
    }
    // 持买仓可买 (3): 持仓 >=4小时 && Bar(0) main 上穿 signal && signal <=30 && Bar(1) main <= signal
    if( (now - lastEnteredBuyable) >= MIN_HOLD_SECONDS && bar0_up_cross && sig0 <= 30.0 && (main1 <= sig1) ){
      if(trendState != 3){
        trendState = 3;
        Print("State -> HOLD_CAN_BUY (3)");
      }
      return;
    }
    // 持买仓等待 (5): 当 main 继续低于20 且上穿 signal 则继续为可买状态, 当 signal ∈ (30,70) 则为买入持有状态
    if( main0 < 20.0 && bar0_up_cross ){
      if(trendState != 5){
        trendState = 5;
        Print("State -> HOLD_BUY_WAIT (5)");
      }
      return;
    }
    // 默认保持当前持有多头状态（不强制改变）
    return;
  }

  // 如果持有空头
  double sellLots = TotalLotsSell();
  if(sellLots > 0.0){
    // 持卖仓可平仓 (8): 持仓 >=4小时 && Bar(0) main 上穿 signal && signal <=30 && Bar(1) main <= signal
    if( (now - lastEnteredSellable) >= MIN_HOLD_SECONDS && bar0_up_cross && sig0 <= 30.0 && (main1 <= sig1) ){
      if(trendState != 8){
        trendState = 8;
        Print("State -> HOLD_SELL_CLOSEABLE (8)");
      }
      return;
    }
    // 持卖仓可卖 (4): 持仓 >=4小时 && Bar(0) main 下穿 signal && signal >=70 && Bar(1) main >= signal
    if( (now - lastEnteredSellable) >= MIN_HOLD_SECONDS && bar0_down_cross && sig0 >= 70.0 && (main1 >= sig1) ){
      if(trendState != 4){
        trendState = 4;
        Print("State -> HOLD_CAN_SELL (4)");
      }
      return;
    }
    // 持卖仓等待 (6): 当 main 继续高于80 且下穿 signal 则继续为卖出持有, 当 signal ∈ (30,70) 则持有
    if( main0 > 80.0 && bar0_down_cross ){
      if(trendState != 6){
        trendState = 6;
        Print("State -> HOLD_SELL_WAIT (6)");
      }
      return;
    }
    return;
  }
}

//+------------------------------------------------------------------+
//| Check MA conditions on M5 and perform trade actions              |
//+------------------------------------------------------------------+
void CheckTradingSignals()
{
  // M5 MA current and previous
  double e0 = MA_e(0), b0 = MA_b(0), d0 = MA_d(0), a0 = MA_a(0);
  double e1 = MA_e(1), b1 = MA_b(1), d1 = MA_d(1), a1 = MA_a(1);

  bool e_cross_b_up = (e0 > b0) && (e1 <= b1);
  bool d_cross_a_down = (d0 < a0) && (d1 >= a1);
  bool e_cross_b_down = (e0 < b0) && (e1 >= b1);
  bool d_cross_a_up = (d0 > a0) && (d1 <= a1);

  // 空仓买入 (state 1)
  if(trendState == 1 && OrdersTotalByMagic() == 0 && e_cross_b_up){
    if(TotalLotsBuy() + Lots <= MarketInfo(Symbol(), MODE_MAXLOT) ){
      OpenOrder(OP_BUY);
      lastEnteredBuyable = TimeCurrent();
      addsCountBuy = 0;
      return;
    }
  }

  // 空仓卖出 (state 2)
  if(trendState == 2 && OrdersTotalByMagic() == 0 && d_cross_a_down){
    if(TotalLotsSell() + Lots <= MarketInfo(Symbol(), MODE_MAXLOT) ){
      OpenOrder(OP_SELL);
      lastEnteredSellable = TimeCurrent();
      addsCountSell = 0;
      return;
    }
  }

  // 持买仓可买 (state 3) - 加仓
  if(trendState == 3 && e_cross_b_up && TotalLotsBuy() > 0.0){
    if(addsCountBuy < MaxAdds){
      OpenOrder(OP_BUY);
      addsCountBuy++;
    } else {
      // 达到最大加仓次数
    }
    return;
  }

  // 持卖仓可卖 (state 4) - 加仓空头
  if(trendState == 4 && d_cross_a_down && TotalLotsSell() > 0.0){
    if(addsCountSell < MaxAdds){
      OpenOrder(OP_SELL);
      addsCountSell++;
    } else {
      // 达到最大加仓次数
    }
    return;
  }

  // 持买仓可平仓 (7)
  if(trendState == 7 && e_cross_b_down && TotalLotsBuy() > 0.0){
    CloseOrders(OP_BUY, Lots);
    if(TotalLotsBuy() <= 0.000001){
      // 恢复空仓等待
      trendState = 0;
      Print("After closing buys -> WAIT_EMPTY (0)");
    }
    return;
  }

  // 持卖仓可平仓 (8)
  if(trendState == 8 && d_cross_a_up && TotalLotsSell() > 0.0){
    CloseOrders(OP_SELL, Lots);
    if(TotalLotsSell() <= 0.000001){
      trendState = 0;
      Print("After closing sells -> WAIT_EMPTY (0)");
    }
    return;
  }
}

//+------------------------------------------------------------------+
//| OpenOrder with SL/TP and retry                                    |
//+------------------------------------------------------------------+
bool OpenOrder(int type)
{
  RefreshRates();
  double price = (type==OP_BUY)? Ask : Bid;
  double sl = (type==OP_BUY)? price - StopLossPips * point : price + StopLossPips * point;
  double tp = (type==OP_BUY)? price + TakeProfitPips * point : price - TakeProfitPips * point;

  int tries = 0;
  while(tries < MaxSendRetries){
    int ticket = OrderSend(Symbol(), type, NormalizeLot(Lots), price, Slippage, sl, tp, "EA", MagicNumber, 0, clrGreen);
    if(ticket > 0){
      PrintFormat("OpenOrder success ticket=%d type=%d lots=%.2f", ticket, type, Lots);
      return true;
    } else {
      int err = GetLastError();
      PrintFormat("OrderSend failed (attempt %d/%d) err=%d", tries+1, MaxSendRetries, err);
      ResetLastError();
      Sleep(RetryDelayMs);
      tries++;
    }
  }
  Print("OpenOrder ultimately failed");
  return false;
}

//+------------------------------------------------------------------+
//| CloseOrders: close up to lotsToClose for given type              |
//+------------------------------------------------------------------+
void CloseOrders(int type, double lotsToClose)
{
  double remaining = lotsToClose;
  for(int i=OrdersTotal()-1;i>=0 && remaining>0.000001;i--){
    if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES)){
      if(OrderMagicNumber()==MagicNumber && OrderSymbol()==Symbol() && OrderType()==type){
        double closeLots = MathMin(OrderLots(), remaining);
        int tries = 0;
        bool closed = false;
        while(tries < MaxCloseRetries && !closed){
          RefreshRates();
          double price = (type==OP_BUY)? Bid : Ask;
          if(OrderClose(OrderTicket(), closeLots, price, Slippage, clrBlue)){
            PrintFormat("Closed ticket=%d lots=%.2f", OrderTicket(), closeLots);
            closed = true;
            remaining -= closeLots;
          } else {
            int err = GetLastError();
            PrintFormat("OrderClose failed (attempt %d/%d) ticket=%d err=%d", tries+1, MaxCloseRetries, OrderTicket(), err);
            ResetLastError();
            Sleep(RetryDelayMs);
            tries++;
          }
        }
        if(!closed){
          PrintFormat("Failed to close ticket %d after retries", OrderTicket());
        }
      }
    }
  }
}

//+------------------------------------------------------------------+
//| Normalize Lot to broker step                                      |
//+------------------------------------------------------------------+
double NormalizeLot(double lots)
{
  double step = MarketInfo(Symbol(), MODE_LOTSTEP);
  double minlot = MarketInfo(Symbol(), MODE_MINLOT);
  double maxlot = MarketInfo(Symbol(), MODE_MAXLOT);
  if(step <= 0) step = 0.01;
  double res = MathFloor(lots/step + 0.000001) * step;
  if(res < minlot) res = minlot;
  if(res > maxlot) res = maxlot;
  return(NormalizeDouble(res,2));
}

//+------------------------------------------------------------------+
//| Smart Trailing Stops (High/Low + MA)                             |
//+------------------------------------------------------------------+
void SmartTrailingStops()
{
  for(int i=OrdersTotal()-1; i>=0; i--){
    if(!OrderSelect(i, SELECT_BY_POS, MODE_TRADES)) continue;
    if(OrderMagicNumber() != MagicNumber || OrderSymbol() != Symbol()) continue;

    int type = OrderType();
    double openPrice = OrderOpenPrice();
    double curSL = OrderStopLoss();
    double curTP = OrderTakeProfit();

    // 计算当前浮盈（pips）
    double profitPips = (type==OP_BUY) ? (Bid - openPrice)/point : (openPrice - Ask)/point;
    if(profitPips < TrailingActivatePips) continue; // 未达到激活点，跳过

    if(type == OP_BUY){
      // 最近 N 根最低价
      int idxLow = iLowest(NULL, PERIOD_M5, MODE_LOW, TrailingLookbackBars, 1);
      double lowestLow = Low[idxLow >=0 ? idxLow : 1];
      double maMedian = iMA(NULL, PERIOD_M5, MA6_period, 0, MODE_SMA, PRICE_MEDIAN, 0);
      double buffer = TrailingBufferPips * point;
      double newSL = MathMax(lowestLow, maMedian - buffer);

      // 要求 newSL > current SL + 小量（避免频繁微调）
      if(newSL > curSL + point){
        // Apply OrderModify with retries
        int tries = 0;
        bool modOK = false;
        while(tries < MaxSendRetries && !modOK){
          RefreshRates();
          modOK = OrderModify(OrderTicket(), openPrice, newSL, curTP, 0, clrYellow);
          if(!modOK){
            int err = GetLastError();
            PrintFormat("OrderModify failed (attempt %d/%d) ticket=%d err=%d", tries+1, MaxSendRetries, OrderTicket(), err);
            ResetLastError();
            Sleep(RetryDelayMs);
          }
          tries++;
        }
        if(modOK) PrintFormat("Updated BUY SL ticket=%d newSL=%.5f", OrderTicket(), newSL);
      }
    }
    else if(type == OP_SELL){
      int idxHigh = iHighest(NULL, PERIOD_M5, MODE_HIGH, TrailingLookbackBars, 1);
      double highestHigh = High[idxHigh >=0 ? idxHigh : 1];
      double maMedianFast = iMA(NULL, PERIOD_M5, MA2_period, 0, MODE_SMA, PRICE_MEDIAN, 0);
      double buffer = TrailingBufferPips * point;
      double newSL = MathMin(highestHigh, maMedianFast + buffer);

      if(curSL == 0.0 || newSL < curSL - point){
        int tries = 0;
        bool modOK = false;
        while(tries < MaxSendRetries && !modOK){
          RefreshRates();
          modOK = OrderModify(OrderTicket(), openPrice, newSL, curTP, 0, clrYellow);
          if(!modOK){
            int err = GetLastError();
            PrintFormat("OrderModify failed (attempt %d/%d) ticket=%d err=%d", tries+1, MaxSendRetries, OrderTicket(), err);
            ResetLastError();
            Sleep(RetryDelayMs);
          }
          tries++;
        }
        if(modOK) PrintFormat("Updated SELL SL ticket=%d newSL=%.5f", OrderTicket(), newSL);
      }
    }
  }
}

//+------------------------------------------------------------------+
