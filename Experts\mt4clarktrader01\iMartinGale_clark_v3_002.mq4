//+------------------------------------------------------------------+
//|                                                iMartinGale_clark_v1.mq4 |
//|                        Copyright 2018, MetaQuotes Software Corp. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2018, MetaQuotes Software Corp."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property strict

// Input parameters
input int    Magic          = 888;
input int    MaxSym         = 2;
input double MaxLots        = 1;
input double MinLots        = 0.05;
input int    StepLots       = 2;
input int    GAP_MT        = 200;

// Indicators Parameters
input int    ATR_PERIOD    = 20;
input double SAR_STEP      = 0.04;
input double SAR_MAX       = 0.2;
input int    STOCH_K       = 24;
input int    STOCH_D       = 12;
input int    STOCH_Slow    = 6;
input int    STOCH_Method  = MODE_SMA;
input int    STOCH_Price   = 1;  // Close/Close;

// New parameters for optimization
input int    MA_Period = 200;  // Trend filter MA period
input int    MA_Method = MODE_EMA; // MA method (0=SMA,1=EMA,2=SMMA,3=LWMA)
input bool   UseTrendFilter = true; // Enable trend filter
input double ATR_Multiplier = 1.5; // ATR multiplier for dynamic stop
input bool   UseDynamicStop = true; // Enable dynamic stop based on ATR

// Global variables
double K_Spread = 20;
string COMM = "iRobert_"+Symbol();

int totalorders =0;
double Lots =0.0,BuyLots=0.0,SellLots=0.0,LastBuyLots=0.0,LastSellLots=0.0;
double BuyPriceHigh =0.0,BuyPriceLow =0.0,SellPriceHigh=0.0,SellPriceLow=0.0;

double ATR_HighLevel=0,ATR_LowLevel=0,ATR_MidLevel=0;
int SAR_K =0, SAR_UPDW = 0;

int ticket=0;
bool ret =true;

bool isNewBarM5 = false,isNewBarH4 = false;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
  K_Spread = MarketInfo(Symbol(),MODE_SPREAD)*Point;

  if( StringFind(Symbol(),"EURUSD",0) )
  {
    GlobalVariableSet("EURUSD",True);
  }

  if( StringFind(Symbol(),"AUDUSD",0) )
  {
    GlobalVariableSet("AUDUSD",True);
  }

  if( StringFind(Symbol(),"GBPUSD",0) )
  {
    GlobalVariableSet("GBPUSD",True);
  }

  if( StringFind(Symbol(),"USDJPY",0) )
  {
    GlobalVariableSet("USDJPY",True);
  }

  if( StringFind(Symbol(),"USDCHF",0) )
  {
    GlobalVariableSet("USDCHF",True);
  }

  if( StringFind(Symbol(),"USDCAD",0) )
  {
    GlobalVariableSet("USDCAD",True);
  }

  return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
  GlobalVariablesDeleteAll(NULL,0);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
  isNewBarM5 = false;isNewBarH4 = false;

  if(iClose(Symbol(),PERIOD_M5,0) == iOpen(Symbol(),PERIOD_M5,0) && iClose(Symbol(),PERIOD_M5,0) == iLow(Symbol(),PERIOD_M5,0) && iClose(Symbol(),PERIOD_M5,0) == iHigh(Symbol(),PERIOD_M5,0)) isNewBarM5 = True;
  if(iClose(Symbol(),PERIOD_H4,0) == iOpen(Symbol(),PERIOD_H4,0) && iClose(Symbol(),PERIOD_H4,0) == iLow(Symbol(),PERIOD_H4,0) && iClose(Symbol(),PERIOD_H4,0) == iHigh(Symbol(),PERIOD_H4,0)) isNewBarH4 = True;

  CheckTotalOrders();
  CheckOpenAtBegin();
  CheckMartingale();
}

//+------------------------------------------------------------------+
//| Open First Position function                                     |
//+------------------------------------------------------------------+
int CheckOpenAtBegin()
{
  if(totalorders>0)return 0;
  CheckBuySellOnInd();

  return 0;
}

//+------------------------------------------------------------------+
//| Martingale function                                              |
//+------------------------------------------------------------------+
int CheckMartingale()
{
  int i=0;
  double P=0;

  if(totalorders==0)
  {
    return 0;
  }

  if(Lots<MaxLots)
  {
    if (BuyLots>0  && Bid<=(BuyPriceLow-GAP_MT*1.5*Point-K_Spread*Point) && BuyOK())
    {
      ticket = OrderSend(Symbol(),OP_BUY,MinLots*MathPow(2,totalorders),Ask,3,0,0,COMM,Magic,0,clrGreen);
      if(ticket<0){
        Print("OrderBuy Failed with error #",GetLastError());
        return 0;
      }
      for(i=0;i<OrdersTotal();i++){
        ret = OrderSelect(i,SELECT_BY_POS);
        if(OrderMagicNumber()==Magic){
          ret=OrderModify(OrderTicket(),OrderOpenPrice(),0,NormalizeDouble(Ask+Point*GAP_MT*1.5,Digits),0,clrGreen);
        }
      }
    }

    if (SellLots>0  && Ask>=(SellPriceHigh+GAP_MT*1.5*Point+K_Spread*Point) && SellOK())
    {
      ticket = OrderSend(Symbol(),OP_SELL,MinLots*MathPow(2,totalorders),Bid,3,0,0,COMM,Magic,0,clrRed);
      if(ticket<0){
        Print("OrderSell Failed with error #",GetLastError());
        return 0;
      }
      for(i=0;i<OrdersTotal();i++){
        ret = OrderSelect(i,SELECT_BY_POS);
        if(OrderMagicNumber()==Magic){
          ret=OrderModify(OrderTicket(),OrderOpenPrice(),0,NormalizeDouble(Bid-Point*GAP_MT*1.5,Digits),0,clrRed);
        }
      }
    }
  }
  return 0;
}

//+------------------------------------------------------------------+
//| Check Open Orders function                                      |
//+------------------------------------------------------------------+
int CheckTotalOrders()
{
  int i=0;

  totalorders =0;
  Lots =0.0;BuyLots=0.0;SellLots=0.0;LastBuyLots=0.0;LastSellLots=0.0;
  BuyPriceHigh =0.0; BuyPriceLow =0.0; SellPriceHigh=0.0; SellPriceLow=0.0;

  if(OrdersTotal()>0){
    for(i=1; i<=OrdersTotal(); i++)  // Cycle searching in orders
    {
      if(OrderSelect(i-1,SELECT_BY_POS)) // If the next is available
      {
        if(OrderMagicNumber()==Magic)
        {
          if(OrderType() == OP_BUY)
          {
            totalorders++;
            Lots = Lots + OrderLots();
            BuyLots = BuyLots + OrderLots();

            if(BuyPriceHigh!=0.0){
              BuyPriceHigh = MathMax(BuyPriceHigh,OrderOpenPrice());
            }else BuyPriceHigh = OrderOpenPrice();

            if(BuyPriceLow !=0.0){
              BuyPriceLow = MathMin(BuyPriceLow,OrderOpenPrice());
            }else BuyPriceLow  = OrderOpenPrice();
          }

          if(OrderType() == OP_SELL)
          {
            totalorders++;
            Lots = Lots + OrderLots();
            SellLots = SellLots + OrderLots();

            if(SellPriceHigh!=0.0){
              SellPriceHigh = MathMax(SellPriceHigh,OrderOpenPrice());
            }else SellPriceHigh = OrderOpenPrice();

            if(SellPriceLow !=0.0){
              SellPriceLow = MathMin(SellPriceLow,OrderOpenPrice());
            }else SellPriceLow  = OrderOpenPrice();
          }
        }
      }
    }
  }
  return 0;
}

//+------------------------------------------------------------------+
//| Check Open Position function 1:Buy;-1:Sell                      |
//+------------------------------------------------------------------+
int CheckBuySellOnInd()
{
  // Buy : SAR 
  if( (Ask-Bid) >= 2.5*K_Spread)
  {
    Print("Spread too high! Normal is :",NormalizeDouble(K_Spread,5),"   Now is :",NormalizeDouble(Ask-Bid,5));
  }
  SAR_K = CheckSAR();
  CheckATR();

  if(SAR_K<3){
    if(SAR_UPDW == -1){
      if(!Checkhedge(Symbol(),OP_BUY))return 0;
      ticket = OrderSend(Symbol(),OP_BUY,MinLots,Ask,3,0,0,COMM,Magic,0,clrGreen);
      if(ticket<0){
        Print("OrderBuy Failed with error #",GetLastError());
        return 0;
      }
    }

    if(SAR_UPDW == 1){
      if(!Checkhedge(Symbol(),OP_SELL))return 0;
      ticket = OrderSend(Symbol(),OP_SELL,MinLots,Bid,3,0,0,COMM,Magic,0,clrRed);
      if(ticket<0){
        Print("OrderSell Failed with error #",GetLastError());
        return 0;
      }
    }
  }

  return 0;
}

bool Checkhedge(string sym,int op)
{
  return(true);
}

//+------------------------------------------------------------------+
//| Check SAR function                                               |
//+------------------------------------------------------------------+
int CheckSAR()
{
  int i,k=0;
  SAR_K =0; SAR_UPDW = 0;
  double mSAR=iSAR(Symbol(),0,SAR_STEP,SAR_MAX,i),nSAR=iSAR(Symbol(),0,SAR_STEP,SAR_MAX,i+1);

  if (mSAR>Close[i] && nSAR<Close[i+1]){ k=1;SAR_UPDW =  1;};
  if (mSAR<Close[i] && nSAR>Close[i+1]){ k=1;SAR_UPDW = -1;};

  SAR_K = k;
  return k;
}

//+------------------------------------------------------------------+
//| Check ATR function                                               |
//+------------------------------------------------------------------+
double CheckATR()
{
  int i;
  double nATR=0,mATR = iATR(Symbol(),0,ATR_PERIOD,0);
  ATR_HighLevel =0;ATR_LowLevel =0;ATR_MidLevel =0;
  for(i=0;i<=864;i++){
    ATR_HighLevel = MathMax(ATR_HighLevel,iATR(Symbol(),0,ATR_PERIOD,i));
    ATR_LowLevel  = MathMin(ATR_LowLevel,iATR(Symbol(),0,ATR_PERIOD,i));
  }

  ATR_MidLevel = (ATR_HighLevel + ATR_LowLevel)/2;
  return ATR_MidLevel;
}

//+------------------------------------------------------------------+
//| Check Buy and Sell direction (get interest) function             |
//+------------------------------------------------------------------+
bool BuyOK()
{
  if(Magic ==11 || Magic ==22 || Magic ==33)
    return(true);
  return(true);
}

bool SellOK()
{
  if(Magic ==1 || Magic ==2 || Magic ==3)
    return(true);
  return(true);
}
//+------------------------------------------------------------------+