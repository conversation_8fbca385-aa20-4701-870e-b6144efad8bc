//+------------------------------------------------------------------+
//|                                                 MartingaleEA.mq4 |
//|                        Copyright 2025, MetaQuotes Software Corp. |
//|                                              https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, MetaQuotes Software Corp."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property strict
#include <stdlib.mqh>
#include <StdLibErr.mqh>
//--- Input parameters
extern double InitialLots    = 0.01; // Initial lot size
extern double LotMultiplier  = 1.5;  // Lot size multiplier after loss (reduced from 2.0)
extern int    MaxTrades      = 5;    // Maximum number of open trades
extern int    TakeProfitPips = 100;   // Take Profit in pips
extern int    StopLossPips   = 250;  // Stop Loss in pips
extern int    MagicNumber    = 12345; // Magic number for orders
extern double MaxRiskPercent = 5.0;  // Maximum account risk percentage
extern bool   UseATRStopLoss = true; // Use ATR for dynamic stop loss
extern int    ATR_Period     = 14;   // ATR period for dynamic stop loss
extern double ATR_Multiplier = 1.5;  // ATR multiplier for stop loss calculation
extern double MaxDrawdownPercent = 20.0; // Maximum drawdown percentage allowed

//--- Indicator Inputs
extern int    MACD_FastEMA   = 12;   // MACD Fast EMA period
extern int    MACD_SlowEMA   = 26;   // MACD Slow EMA period
extern int    MACD_SignalSMA = 9;    // MACD Signal SMA period
extern int    CCI_Period     = 14;   // CCI Period
extern double CCI_Level_Buy  = 100;  // CCI level for Buy signal threshold
extern double CCI_Level_Sell = -100; // CCI level for Sell signal threshold
extern int    MA_Period      = 200;  // Moving Average period for trend filter
extern int    MA_Method      = MODE_EMA; // Moving Average method (0=SMA, 1=EMA, 2=SMMA, 3=LWMA)
extern bool   UseStrictTrendFilter = true; // Enable strict trend filtering

//--- Global variables
int    totalOrders = 0;
double currentLots = 0;
int    tradeDirection = OP_BUY; // Initial trade direction (can be changed or made dynamic)
double lastOrderPrice = 0;      // Price of the last opened order in the sequence
int    orderSequenceCount = 0; // Counter for the Martingale sequence

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
  {
//---
   Print("Martingale EA Initializing...");
   currentLots = InitialLots;
   orderSequenceCount = 0;
//---
   return(INIT_SUCCEEDED);
  }
//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
  {
//---
   Print("Martingale EA Deinitializing...");
   // Optional: Close all open orders on deinitialization
   // CloseAllOrders();
//---
  }
//+------------------------------------------------------------------+
//| Check if market conditions are suitable for trading              |
//+------------------------------------------------------------------+
bool IsMarketConditionSuitable()
  {
   // Check for high spread
   double currentSpread = MarketInfo(Symbol(), MODE_SPREAD) * Point;
   double maxAllowedSpread = 100 * Point; // Maximum allowed spread (adjust as needed)
   
   if(currentSpread > maxAllowedSpread)
     {
      Print("Current spread (", DoubleToString(currentSpread/Point, 1), 
            " points) exceeds maximum allowed (", DoubleToString(maxAllowedSpread/Point, 1), " points)");
      return false;
     }
     
   // Check for high volatility using ATR
   double atr = iATR(NULL, 0, ATR_Period, 0);
   double normalATR = 0;
   
   // Calculate average ATR over last 20 periods
   for(int i=1; i<=20; i++)
     {
      normalATR += iATR(NULL, 0, ATR_Period, i);
     }
   normalATR /= 20;
   
   // If current ATR is more than 2x the normal ATR, market is too volatile
   if(atr > 2.0 * normalATR)
     {
      Print("Market volatility too high. Current ATR: ", DoubleToString(atr, 5), 
            ", Normal ATR: ", DoubleToString(normalATR, 5));
      return false;
     }
     
   // Check for important news events (simplified - would need external data source for real implementation)
   // This is a placeholder for a more sophisticated news filter
   int hour = Hour();
   int minute = Minute();
   int dayOfWeek = DayOfWeek();
   
   // Avoid trading during typical high-impact news times (e.g., NFP first Friday of month)
   if(dayOfWeek == 5 && Day() <= 7 && hour == 12 && minute >= 30 && minute <= 45)
     {
      Print("Potential high-impact news time. Avoiding trade.");
      return false;
     }
     
   return true;
  }

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
  {
//--- Check if we can trade
   if(!IsTradeAllowed()) return;
   if(IsStopped()) return; // Check if EA is stopped
   if(!IsMarketConditionSuitable()) return; // Check if market conditions are suitable

   totalOrders = CountOrders();

   // If no orders exist, check for entry signals to start a new sequence
   if(totalOrders == 0)
     {
      orderSequenceCount = 0; // Reset sequence counter
      currentLots = InitialLots; // Reset lots

      // --- Calculate Indicator Values ---
      double macd_main_curr = iMACD(NULL, 0, MACD_FastEMA, MACD_SlowEMA, MACD_SignalSMA, PRICE_CLOSE, MODE_MAIN, 0);
      double macd_signal_curr = iMACD(NULL, 0, MACD_FastEMA, MACD_SlowEMA, MACD_SignalSMA, PRICE_CLOSE, MODE_SIGNAL, 0);
      double macd_main_prev = iMACD(NULL, 0, MACD_FastEMA, MACD_SlowEMA, MACD_SignalSMA, PRICE_CLOSE, MODE_MAIN, 1);
      double macd_signal_prev = iMACD(NULL, 0, MACD_FastEMA, MACD_SlowEMA, MACD_SignalSMA, PRICE_CLOSE, MODE_SIGNAL, 1);

      double cci_curr = iCCI(NULL, 0, CCI_Period, PRICE_TYPICAL, 0);
      double cci_prev = iCCI(NULL, 0, CCI_Period, PRICE_TYPICAL, 1); // Previous CCI value
      
      // --- Add Trend Filter ---
      double ma_current = iMA(NULL, 0, MA_Period, 0, MA_Method, PRICE_CLOSE, 0);
      double ma_prev = iMA(NULL, 0, MA_Period, 0, MA_Method, PRICE_CLOSE, 10); // Compare with 10 bars ago
      
      // --- Add ATR for volatility measurement ---
      double atr = iATR(NULL, 0, ATR_Period, 0);
      double avg_atr = 0;
      // Calculate average ATR over last 5 bars
      for(int i=0; i<5; i++)
      {
         avg_atr += iATR(NULL, 0, ATR_Period, i);
      }
      avg_atr /= 5;
      
      // Determine if market volatility is suitable for trading
      bool volatilityOK = (atr > 0.5*avg_atr && atr < 2.0*avg_atr); // Avoid extremely low or high volatility
      
      // Determine trend direction
      bool uptrend = Close[0] > ma_current && ma_current > ma_prev;
      bool downtrend = Close[0] < ma_current && ma_current < ma_prev;
      
      // --- Define Entry Conditions with Trend Filter ---
      // Buy Signal: MACD cross up AND CCI confirms AND in uptrend (or not in strict downtrend)
      bool buySignal = (macd_main_curr > macd_signal_curr && macd_main_prev <= macd_signal_prev) && // MACD Cross Up
                       (cci_curr > CCI_Level_Buy || (cci_curr > cci_prev && cci_curr > 0)) && // CCI confirms
                       (uptrend || (!UseStrictTrendFilter && !downtrend)) && // Trend filter
                       volatilityOK; // Volatility check

      // Sell Signal: MACD cross down AND CCI confirms AND in downtrend (or not in strict uptrend)
      bool sellSignal = (macd_main_curr < macd_signal_curr && macd_main_prev >= macd_signal_prev) && // MACD Cross Down
                        (cci_curr < CCI_Level_Sell || (cci_curr < cci_prev && cci_curr < 0)) && // CCI confirms
                        (downtrend || (!UseStrictTrendFilter && !uptrend)) && // Trend filter
                        volatilityOK; // Volatility check


      // --- Place Initial Order based on Signals ---
      if(buySignal)
        {
         tradeDirection = OP_BUY; // Set sequence direction
         Print("MACD/CCI Buy Signal detected. Placing initial Buy order.");
         PlaceOrder(tradeDirection, currentLots);
        }
      else if(sellSignal)
        {
         tradeDirection = OP_SELL; // Set sequence direction
         Print("MACD/CCI Sell Signal detected. Placing initial Sell order.");
         PlaceOrder(tradeDirection, currentLots);
        }
      // else: No signal, do nothing, wait for next tick
     }
   else // Orders exist, manage the Martingale sequence
     {
      // Check if the sequence should be closed (e.g., reached overall TP)
      if(CheckOverallProfit())
        {
         CloseAllOrders();
        }
      else
        {
         // Check if a new order needs to be placed (Martingale step)
         // This basic version assumes a loss triggers the next order.
         // A more robust version would check the status of the *last* order.
         // For simplicity here, we'll add a new order if the count is less than MaxTrades
         // and assume the previous one was a loss (needs refinement).
         // A better trigger would be needed in a real system (e.g., last order closed at SL).

         // Let's refine this: Check if the *last* order closed at a loss.
         // This requires tracking closed orders, which adds complexity.
         // Alternative: Place next order if price moves against the last order by X pips.
         // Let's use the price movement trigger for simplicity now.

         if(orderSequenceCount > 0 && orderSequenceCount < MaxTrades) // Check if we are in a sequence and below max trades
           {
            double pips_factor = PipValue();
            double price_diff = 0;

            if(tradeDirection == OP_BUY)
              {
               price_diff = lastOrderPrice - Bid; // Price moved down against our Buy
              }
            else // OP_SELL
              {
               price_diff = Ask - lastOrderPrice; // Price moved up against our Sell
              }

            // Dynamic trigger based on ATR and market conditions
            double atr = iATR(NULL, 0, ATR_Period, 0);
            double dynamicTriggerPips = MathRound(atr / Point / 10) * 10; // Round to nearest 10 pips
            
            // Ensure trigger is within reasonable bounds
            int minTriggerPips = 30;
            int maxTriggerPips = 100;
            
            if(dynamicTriggerPips < minTriggerPips) dynamicTriggerPips = minTriggerPips;
            if(dynamicTriggerPips > maxTriggerPips) dynamicTriggerPips = maxTriggerPips;
            
            // Check if we should place the next Martingale order
            if(price_diff / pips_factor >= dynamicTriggerPips)
              {
               // Check if market is still moving in the expected direction for our strategy
               bool continueSequence = true;
               
               // Check trend direction before adding to position
               double ma_current = iMA(NULL, 0, MA_Period, 0, MA_Method, PRICE_CLOSE, 0);
               double ma_prev = iMA(NULL, 0, MA_Period, 0, MA_Method, PRICE_CLOSE, 10);
               
               // Only continue Martingale if trend still supports our position
               if(tradeDirection == OP_BUY && ma_current < ma_prev && UseStrictTrendFilter)
                 {
                  Print("Trend has turned bearish. Skipping Martingale step.");
                  continueSequence = false;
                 }
               else if(tradeDirection == OP_SELL && ma_current > ma_prev && UseStrictTrendFilter)
                 {
                  Print("Trend has turned bullish. Skipping Martingale step.");
                  continueSequence = false;
                 }
               
               if(continueSequence)
                 {
                  // Use a more conservative multiplier as we go deeper into the sequence
                  double adjustedMultiplier = LotMultiplier;
                  if(orderSequenceCount > 2)
                    {
                     // Reduce multiplier for later trades to manage risk
                     adjustedMultiplier = 1.0 + (LotMultiplier - 1.0) * (1.0 - (orderSequenceCount - 2.0) / MaxTrades);
                     if(adjustedMultiplier < 1.2) adjustedMultiplier = 1.2; // Minimum multiplier
                    }
                  
                  currentLots = currentLots * adjustedMultiplier;
                  
                  // Add checks for max lot size allowed by broker
                  double maxLot = MarketInfo(Symbol(), MODE_MAXLOT);
                  if(currentLots > maxLot) currentLots = maxLot;
                  
                  Print("Placing Martingale order #", (orderSequenceCount+1), " with dynamic trigger: ", 
                        dynamicTriggerPips, " pips, adjusted multiplier: ", DoubleToString(adjustedMultiplier, 2));
                  
                  PlaceOrder(tradeDirection, currentLots);
                 }
              }
           }
        }
     }
//---
  }

//+------------------------------------------------------------------+
//| Calculate Pip Value                                             |
//+------------------------------------------------------------------+
double PipValue()
  {
   if(Digits == 5 || Digits == 3) return(Point * 10);
   else return(Point);
  }

//+------------------------------------------------------------------+
//| Count open orders for this EA                                    |
//+------------------------------------------------------------------+
int CountOrders()
  {
   int count = 0;
   for(int i = OrdersTotal() - 1; i >= 0; i--)
     {
      if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
        {
         if(OrderSymbol() == Symbol() && OrderMagicNumber() == MagicNumber)
           {
            count++;
           }
        }
     }
   return(count);
  }

//+------------------------------------------------------------------+
//| Place a new order                                                |
//+------------------------------------------------------------------+
bool PlaceOrder(int direction, double lots)
  {
   if(orderSequenceCount >= MaxTrades)
     {
      Print("Max trades reached (", MaxTrades, "), cannot place new order.");
      return(false);
     }
     
   // Check account drawdown percentage
   double balance = AccountBalance();
   double equity = AccountEquity();
   double drawdownPercent = (balance - equity) / balance * 100;
   
   if(drawdownPercent > MaxDrawdownPercent)
     {
      Print("Maximum drawdown percentage reached (", DoubleToString(drawdownPercent, 2), "%). Skipping new order.");
      return(false);
     }

   double price = 0;
   double sl = 0;
   double tp = 0;
   double pips = PipValue();

   RefreshRates(); // Ensure fresh prices
   
   // Calculate dynamic stop loss based on ATR if enabled
   double atr_sl = 0;
   if(UseATRStopLoss)
     {
      double atr = iATR(NULL, 0, ATR_Period, 0);
      atr_sl = atr * ATR_Multiplier / Point;
      // Ensure minimum stop loss
      if(atr_sl < StopLossPips) atr_sl = StopLossPips;
     }
   else
     {
      atr_sl = StopLossPips;
     }

   if(direction == OP_BUY)
     {
      price = Ask;
      if(atr_sl > 0) sl = price - atr_sl * pips;
      if(TakeProfitPips > 0) tp = price + TakeProfitPips * pips;
     }
   else if(direction == OP_SELL)
     {
      price = Bid;
      if(atr_sl > 0) sl = price + atr_sl * pips;
      if(TakeProfitPips > 0) tp = price - TakeProfitPips * pips;
     }
   else
     {
      Print("Invalid trade direction specified: ", direction);
      return(false);
     }
     
   // Risk management - adjust lot size based on account risk percentage
   if(MaxRiskPercent > 0 && sl != 0)
     {
      double riskAmount = AccountBalance() * MaxRiskPercent / 100;
      double pipValue = MarketInfo(Symbol(), MODE_TICKVALUE) * (pips / Point);
      double slDistance = MathAbs((price - sl) / pips);
      double calculatedLots = riskAmount / (slDistance * pipValue);
      
      // If calculated lots is less than current lots, use it for safer trading
      if(calculatedLots < lots)
        {
         lots = calculatedLots;
         Print("Lot size adjusted to ", DoubleToString(lots, 2), " based on risk management");
        }
     }

   // Normalize SL/TP
   sl = NormalizeDouble(sl, Digits);
   tp = NormalizeDouble(tp, Digits);

   // Basic volume check
   lots = NormalizeDouble(lots, 2); // Normalize lots to 2 decimal places
   double minLot = MarketInfo(Symbol(), MODE_MINLOT);
   double maxLot = MarketInfo(Symbol(), MODE_MAXLOT);
   double lotStep = MarketInfo(Symbol(), MODE_LOTSTEP);

   if(lots < minLot) lots = minLot;
   if(lots > maxLot) lots = maxLot;
   // Adjust lots according to step
   lots = MathFloor(lots / lotStep) * lotStep;
   lots = NormalizeDouble(lots, 2);


   if(lots < minLot)
     {
       Print("Lot size ", lots, " is less than minimum allowed ", minLot);
       return(false);
     }


   string comment = StringFormat("Martingale #%d", orderSequenceCount + 1);
   int ticket = OrderSend(Symbol(), direction, lots, price, 3, sl, tp, comment, MagicNumber, 0, (direction == OP_BUY ? clrBlue : clrRed));

   if(ticket > 0)
     {
      Print("OrderSend successful: ticket=", ticket, ", dir=", direction, ", lots=", lots, ", price=", price, ", SL=", sl, ", TP=", tp);
      lastOrderPrice = price; // Store the price of this order
      orderSequenceCount++;   // Increment sequence counter
      currentLots = lots;     // Update current lots for the next potential step
      return(true);
     }
   else
     {
      int error = GetLastError();
      Print("OrderSend failed: Error ", error, " - ", ErrorDescription(error));
      return(false);
     }
  }

//+------------------------------------------------------------------+
//| Check overall profit for the sequence                            |
//+------------------------------------------------------------------+
bool CheckOverallProfit()
  {
   double totalProfit = 0;
   double totalLots = 0;
   double pips = PipValue();
   double targetProfitPips = TakeProfitPips; // Use the input TP as the target for the whole sequence
   int orderCount = 0;

   for(int i = OrdersTotal() - 1; i >= 0; i--)
     {
      if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
        {
         if(OrderSymbol() == Symbol() && OrderMagicNumber() == MagicNumber)
           {
            totalProfit += OrderProfit() + OrderSwap() + OrderCommission();
            totalLots += OrderLots();
            orderCount++;
           }
        }
     }
     
   // No orders to check
   if(orderCount == 0) return false;

   // Calculate required profit based on total investment and risk
   double totalInvestment = 0;
   double breakEvenProfit = 0;
   
   // Calculate total investment and break-even point
   if(orderSequenceCount > 1)
     {
      // For Martingale sequence, we need to recover previous losses
      // Calculate a dynamic profit target based on sequence depth
      double dynamicTarget = 0;
      
      // The deeper we are in the sequence, the more aggressive we should be about taking profits
      if(orderSequenceCount >= MaxTrades)
        {
         // At max trades, take any profit
         dynamicTarget = 0.1; // Take even small profits
        }
      else if(orderSequenceCount > MaxTrades/2)
        {
         // Beyond halfway point, lower profit target
         dynamicTarget = targetProfitPips * 0.5;
        }
      else
        {
         // Early in sequence, maintain normal target
         dynamicTarget = targetProfitPips;
        }
        
      // Calculate profit target in account currency
      double profitTarget = totalLots * dynamicTarget * MarketInfo(Symbol(), MODE_TICKVALUE) / MarketInfo(Symbol(), MODE_TICKSIZE) * Point;
      
      // If we're in profit and above our dynamic target, close positions
      if(totalProfit > 0 && totalProfit >= profitTarget)
        {
         Print("Dynamic profit target reached: ", DoubleToString(totalProfit, 2), 
               " (Target: ", DoubleToString(profitTarget, 2), "). Closing sequence.");
         return(true);
        }
     }
   else
     {
      // For first trade, use standard take profit
      double profitTarget = InitialLots * targetProfitPips * MarketInfo(Symbol(), MODE_TICKVALUE) / MarketInfo(Symbol(), MODE_TICKSIZE) * Point;
      
      if(totalProfit >= profitTarget)
        {
         Print("Profit target reached: ", DoubleToString(totalProfit, 2), ". Closing sequence.");
         return(true);
        }
     }
     
   // Emergency exit - if we're in profit and market is turning against us
   if(totalProfit > 0)
     {
      // Check if market momentum is turning against our position
      bool momentumShift = false;
      
      if(tradeDirection == OP_BUY)
        {
         // For buy positions, check if momentum is turning bearish
         double macd_main = iMACD(NULL, 0, MACD_FastEMA, MACD_SlowEMA, MACD_SignalSMA, PRICE_CLOSE, MODE_MAIN, 0);
         double macd_prev = iMACD(NULL, 0, MACD_FastEMA, MACD_SlowEMA, MACD_SignalSMA, PRICE_CLOSE, MODE_MAIN, 1);
         
         if(macd_main < macd_prev && macd_main < 0)
           {
            momentumShift = true;
           }
        }
      else if(tradeDirection == OP_SELL)
        {
         // For sell positions, check if momentum is turning bullish
         double macd_main = iMACD(NULL, 0, MACD_FastEMA, MACD_SlowEMA, MACD_SignalSMA, PRICE_CLOSE, MODE_MAIN, 0);
         double macd_prev = iMACD(NULL, 0, MACD_FastEMA, MACD_SlowEMA, MACD_SignalSMA, PRICE_CLOSE, MODE_MAIN, 1);
         
         if(macd_main > macd_prev && macd_main > 0)
           {
            momentumShift = true;
           }
        }
        
      if(momentumShift)
        {
         Print("Market momentum shifting against position. Taking current profit: ", DoubleToString(totalProfit, 2));
         return(true);
        }
     }

   return(false);
  }

//+------------------------------------------------------------------+
//| Close all open orders for this EA                                |
//+------------------------------------------------------------------+
void CloseAllOrders()
  {
   Print("Closing all orders for MagicNumber: ", MagicNumber);
   bool result = true;
   int closedCount = 0;

   RefreshRates(); // Get latest prices

   for(int i = OrdersTotal() - 1; i >= 0; i--)
     {
      if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
        {
         if(OrderSymbol() == Symbol() && OrderMagicNumber() == MagicNumber)
           {
            double closePrice = 0;
            if(OrderType() == OP_BUY) closePrice = Bid;
            if(OrderType() == OP_SELL) closePrice = Ask;

            result = OrderClose(OrderTicket(), OrderLots(), closePrice, 3, clrNONE);
            if(result)
              {
               Print("Closed order #", OrderTicket(), " successfully.");
               closedCount++;
              }
            else
              {
               int error = GetLastError();
               Print("Failed to close order #", OrderTicket(), ". Error: ", error, " - ", ErrorDescription(error));
               // Optional: retry logic
              }
            // Small pause to avoid overwhelming the server
            Sleep(100); // 100 milliseconds
           }
        }
     }
   if(closedCount > 0)
     {
      Print("Finished closing orders. ", closedCount, " orders closed.");
      // Reset sequence state after closing
      orderSequenceCount = 0;
      currentLots = InitialLots;
      lastOrderPrice = 0;
     }
   else
     {
       Print("No orders found to close for MagicNumber ", MagicNumber);
     }
  }
//+------------------------------------------------------------------+
