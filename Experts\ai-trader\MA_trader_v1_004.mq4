//+------------------------------------------------------------------+
//| 智能状态机交易EA with Trailing SL (High/Low + MA)                |
//+------------------------------------------------------------------+
#property strict

//---- 输入参数
input double Lots = 0.1;                   // 每次交易手数
input double StopLossPips = 30;            // 初始固定止损
input double TakeProfitPips = 60;          // 初始固定止盈
input int    Slippage = 3;                 // 允许滑点
input int    TrailingLookbackBars = 5;     // 追踪止损计算K线数
input double TrailingBufferPips = 5;       // 高低点与MA止损缓冲
input double TrailingActivatePips = 20;    // 浮盈达到多少点开始追踪
input int    MagicNumber = 123456;         // 订单魔术号

//---- 全局变量
int trendState = 0; // 趋势状态机 (0-8)
double point;

//+------------------------------------------------------------------+
//| 初始化                                                           |
//+------------------------------------------------------------------+
int OnInit()
{
    point = MarketInfo(Symbol(), MODE_POINT);
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| EA 主循环                                                        |
//+------------------------------------------------------------------+
void OnTick()
{
    UpdateTrendState();         // 更新趋势状态机（4H Stochastic）
    CheckTradingSignals();      // 执行开仓、加仓、平仓条件
    SmartTrailingStops();       // 智能追踪止损
}

//+------------------------------------------------------------------+
//| 更新趋势状态机（简化示例，需按你提供的详细条件实现）             |
//+------------------------------------------------------------------+
void UpdateTrendState()
{
    // TODO: 按你给的 0~8 状态机条件完整实现
    // 示例：
    double main0 = iStochastic(NULL, PERIOD_H4, 20, 2, 2, MODE_SMA, 1, MODE_MAIN, 0);
    double signal0 = iStochastic(NULL, PERIOD_H4, 20, 2, 2, MODE_SMA, 1, MODE_SIGNAL, 0);
    double main1 = iStochastic(NULL, PERIOD_H4, 20, 2, 2, MODE_SMA, 1, MODE_MAIN, 1);
    double signal1 = iStochastic(NULL, PERIOD_H4, 20, 2, 2, MODE_SMA, 1, MODE_SIGNAL, 1);

    // 这里只做演示，完整状态判断请替换为你的条件
    if(main0 > signal0 && signal0 <= 30 && main1 <= signal1)
        trendState = 1; // 空仓可买
}

//+------------------------------------------------------------------+
//| 检查交易信号                                                      |
//+------------------------------------------------------------------+
void CheckTradingSignals()
{
    double ma_a = iMA(NULL, PERIOD_M5, 6, 0, MODE_SMA, PRICE_HIGH, 0);
    double ma_b = iMA(NULL, PERIOD_M5, 6, 0, MODE_SMA, PRICE_LOW, 0);
    double ma_e = iMA(NULL, PERIOD_M5, 2, 0, MODE_SMA, PRICE_LOW, 0);
    double ma_d = iMA(NULL, PERIOD_M5, 2, 0, MODE_SMA, PRICE_HIGH, 0);

    if(OrdersTotalByMagic(MagicNumber) == 0)
    {
        if(trendState == 1 && ma_e > ma_b) // 空仓买
            OpenOrder(OP_BUY);
        if(trendState == 2 && ma_d < ma_a) // 空仓卖
            OpenOrder(OP_SELL);
    }
    else
    {
        // 示例：持买仓可平仓
        if(trendState == 7 && ma_e < ma_b)
            CloseOrders(OP_BUY);

        // 示例：持卖仓可平仓
        if(trendState == 8 && ma_d > ma_a)
            CloseOrders(OP_SELL);
    }
}

//+------------------------------------------------------------------+
//| 开仓函数（带止盈止损+错误重试）                                   |
//+------------------------------------------------------------------+
bool OpenOrder(int type)
{
    double price = (type == OP_BUY) ? Ask : Bid;
    double sl = (type == OP_BUY) ? price - StopLossPips * point : price + StopLossPips * point;
    double tp = (type == OP_BUY) ? price + TakeProfitPips * point : price - TakeProfitPips * point;

    return RetryOrderSend(Symbol(), type, Lots, price, Slippage, sl, tp, "", MagicNumber, 0, clrNONE, 3);
}

//+------------------------------------------------------------------+
//| 平仓函数（错误重试）                                             |
//+------------------------------------------------------------------+
void CloseOrders(int type)
{
    for(int i=OrdersTotal()-1; i>=0; i--)
    {
        if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES) && OrderMagicNumber() == MagicNumber && OrderType() == type)
        {
            double closePrice = (type == OP_BUY) ? Bid : Ask;
            RetryOrderClose(OrderTicket(), OrderLots(), closePrice, Slippage, clrNONE, 3);
        }
    }
}

//+------------------------------------------------------------------+
//| 智能追踪止损模块                                                 |
//+------------------------------------------------------------------+
void SmartTrailingStops()
{
    for(int i=OrdersTotal()-1; i>=0; i--)
    {
        if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES) && OrderMagicNumber() == MagicNumber)
        {
            double currentPrice = OrderClosePrice();
            double profitPips = (OrderType()==OP_BUY ? 
                (Bid - OrderOpenPrice())/point : 
                (OrderOpenPrice() - Ask)/point);

            if(profitPips < TrailingActivatePips) continue;

            double newSL = 0;
            if(OrderType() == OP_BUY)
            {
                double lowestLow = Low[iLowest(NULL, PERIOD_M5, MODE_LOW, TrailingLookbackBars, 1)];
                double maMedian = iMA(NULL, PERIOD_M5, 6, 0, MODE_SMA, PRICE_MEDIAN, 0);
                newSL = MathMax(lowestLow, maMedian - TrailingBufferPips*point);

                if(newSL > OrderStopLoss() + point)
                    RetryOrderModify(OrderTicket(), OrderOpenPrice(), newSL, OrderTakeProfit(), 3);
            }
            else if(OrderType() == OP_SELL)
            {
                double highestHigh = High[iHighest(NULL, PERIOD_M5, MODE_HIGH, TrailingLookbackBars, 1)];
                double maMedianFast = iMA(NULL, PERIOD_M5, 2, 0, MODE_SMA, PRICE_MEDIAN, 0);
                newSL = MathMin(highestHigh, maMedianFast + TrailingBufferPips*point);

                if(newSL < OrderStopLoss() - point)
                    RetryOrderModify(OrderTicket(), OrderOpenPrice(), newSL, OrderTakeProfit(), 3);
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 辅助函数：逐笔错误重试                                            |
//+------------------------------------------------------------------+
bool RetryOrderSend(string symbol, int cmd, double volume, double price, int slippage,
                    double stoploss, double takeprofit, string comment, int magic,
                    datetime expiration, color arrow_color, int maxRetries)
{
    int attempt=0;
    while(attempt<maxRetries)
    {
        int ticket = OrderSend(symbol, cmd, volume, price, slippage, stoploss, takeprofit, comment, magic, expiration, arrow_color);
        if(ticket > 0) return true;
        attempt++;
        Sleep(500);
    }
    Print("开仓失败 Type=", cmd);
    return false;
}

bool RetryOrderModify(int ticket, double price, double sl, double tp, int maxRetries)
{
    int attempt=0;
    while(attempt<maxRetries)
    {
        if(OrderModify(ticket, price, sl, tp, 0, clrNONE)) return true;
        attempt++;
        Sleep(500);
    }
    Print("修改止损失败 Ticket=", ticket);
    return false;
}

bool RetryOrderClose(int ticket, double lots, double price, int slippage, color arrow_color, int maxRetries)
{
    int attempt=0;
    while(attempt<maxRetries)
    {
        if(OrderClose(ticket, lots, price, slippage, arrow_color)) return true;
        attempt++;
        Sleep(500);
    }
    Print("平仓失败 Ticket=", ticket);
    return false;
}

//+------------------------------------------------------------------+
//| 统计当前魔术号订单数                                              |
//+------------------------------------------------------------------+
int OrdersTotalByMagic(int magic)
{
    int count = 0;
    for(int i=0; i<OrdersTotal(); i++)
    {
        if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES) && OrderMagicNumber()==magic)
            count++;
    }
    return count;
}
