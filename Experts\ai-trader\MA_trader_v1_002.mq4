//+------------------------------------------------------------------+
//|  EA: Stochastic(H4) trend + MAs(M5) state-machine trading         |
//|  Language: MQL4                                                  |
//|  Author: ChatGPT (skeleton, test before live)                    |
//+------------------------------------------------------------------+
#property strict

// Inputs
input double LotsPerTrade = 0.1;         // 每次开/加仓手数
input int Slippage = 3;
input int MagicNumber = 20250808;
input int MA6_period = 6;
input int MA2_period = 2;

// Stochastic params (H4)
input int Stoch_Kperiod = 20;
input int Stoch_Dperiod = 2;
input int Stoch_Slowing = 2;
input int Stoch_ma_method = MODE_SMA; // MODE_SMA
input int Stoch_price_field = 0;      // 0 = PRICE_CLOSE in iStochastic usage below

// Time thresholds
#define BUYABLE_MIN_DURATION_SECONDS  (4*3600)   // 4 hours
#define SELLABLE_MIN_DURATION_SECONDS (4*3600)

// State definitions
enum StrategyState {
  STATE_WAIT_EMPTY = 0,
  STATE_EMPTY_CAN_BUY = 1,
  STATE_EMPTY_CAN_SELL = 2,
  STATE_HOLD_CAN_BUY = 3,
  STATE_HOLD_CAN_SELL = 4,
  STATE_HOLD_BUY_WAIT = 5,
  STATE_HOLD_SELL_WAIT = 6,
  STATE_HOLD_BUY_CLOSEABLE = 7,
  STATE_HOLD_SELL_CLOSEABLE = 8
};

// Global variables
int currentState = STATE_WAIT_EMPTY;
datetime lastStateChange = 0;
datetime lastEnteredBuyable = 0;   // when entered state 1 or 3
datetime lastEnteredSellable = 0;  // when entered state 2 or 4

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
  currentState = STATE_WAIT_EMPTY;
  lastStateChange = TimeCurrent();
  // nothing special to init
  return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Helper: get Stochastic main/signal on H4 for a given shift       |
//+------------------------------------------------------------------+
double StochMain(int shift)
{
  // iStochastic params:
  // symbol, timeframe, Kperiod, Dperiod, slowing, ma_method, price_field, mode (MODE_MAIN=MODE_MAIN), shift
  return iStochastic(NULL, PERIOD_H4,
                     Stoch_Kperiod, Stoch_Dperiod, Stoch_Slowing,
                     Stoch_ma_method, Stoch_price_field,
                     MODE_MAIN, shift);
}
double StochSignal(int shift)
{
  return iStochastic(NULL, PERIOD_H4,
                     Stoch_Kperiod, Stoch_Dperiod, Stoch_Slowing,
                     Stoch_ma_method, Stoch_price_field,
                     MODE_SIGNAL, shift);
}

//+------------------------------------------------------------------+
//| Helper: get MA values on M5 for different applied prices         |
//+------------------------------------------------------------------+
double MA_value(int period, int ma_method, int applied_price, int shift)
{
  return iMA(NULL, PERIOD_M5, period, 0, ma_method, applied_price, shift);
}

// convenience wrappers for named MAs
double MA_a(int shift){ return MA_value(MA6_period, MODE_SMA, PRICE_HIGH, shift); }   // MA_a (6, high)
double MA_b(int shift){ return MA_value(MA6_period, MODE_SMA, PRICE_LOW, shift); }    // MA_b (6, low)
double MA_c(int shift){ return MA_value(MA6_period, MODE_SMA, PRICE_MEDIAN, shift); } // MA_c (6, median)
double MA_d(int shift){ return MA_value(MA2_period, MODE_SMA, PRICE_HIGH, shift); }   // MA_d (2, high)
double MA_e(int shift){ return MA_value(MA2_period, MODE_SMA, PRICE_LOW, shift); }    // MA_e (2, low)
double MA_f(int shift){ return MA_value(MA2_period, MODE_SMA, PRICE_MEDIAN, shift); } // MA_f (2, median)

//+------------------------------------------------------------------+
//| Utility: count current positions for this EA (buys and sells)    |
//+------------------------------------------------------------------+
int CountMyPositions(bool buy)
{
  int cnt = 0;
  for(int i=0;i<OrdersTotal();i++){
    if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES)){
      if(OrderMagicNumber() == MagicNumber && OrderSymbol() == Symbol()){
        if(buy && OrderType() == OP_BUY) cnt++;
        if(!buy && OrderType() == OP_SELL) cnt++;
      }
    }
  }
  return cnt;
}
double TotalLots(bool buy)
{
  double s = 0.0;
  for(int i=0;i<OrdersTotal();i++){
    if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES)){
      if(OrderMagicNumber() == MagicNumber && OrderSymbol() == Symbol()){
        if(buy && OrderType() == OP_BUY) s += OrderLots();
        if(!buy && OrderType() == OP_SELL) s += OrderLots();
      }
    }
  }
  return s;
}

//+------------------------------------------------------------------+
//| Update the trend state machine based on H4 Stochastic signals    |
//+------------------------------------------------------------------+
void UpdateTrendState()
{
  // Fetch current and previous Stochastic main/signal on H4
  double main0 = StochMain(0);
  double sig0  = StochSignal(0);
  double main1 = StochMain(1);
  double sig1  = StochSignal(1);

  datetime now = TimeCurrent();

  // Utility lambdas (simple inline logic using comments style)
  bool main_up_cross_0 = (main0 > sig0) && (main1 <= sig1);     // Bar(0) main up crossing signal, Bar(1) main <= signal
  bool main_down_cross_0 = (main0 < sig0) && (main1 >= sig1);   // Bar(0) main down crossing signal, Bar(1) main >= signal

  // Evaluate states: prefer closing states if conditions met; else enter buy/sellable states
  // First: empty states
  if (TotalLots(true) + TotalLots(false) < 0.000001) {
    // currently empty
    if ( main_up_cross_0 && sig0 <= 30.0 ) {
      // enter empty can buy
      if(currentState != STATE_EMPTY_CAN_BUY){
        currentState = STATE_EMPTY_CAN_BUY;
        lastStateChange = now;
        lastEnteredBuyable = now;
      }
    } else if ( main_down_cross_0 && sig0 >= 70.0 ) {
      // enter empty can sell
      if(currentState != STATE_EMPTY_CAN_SELL){
        currentState = STATE_EMPTY_CAN_SELL;
        lastStateChange = now;
        lastEnteredSellable = now;
      }
    } else {
      // remain in wait empty
      if(currentState != STATE_WAIT_EMPTY){
        currentState = STATE_WAIT_EMPTY;
        lastStateChange = now;
      }
    }
    return;
  }

  // If we hold buys
  double buyLots = TotalLots(true);
  if(buyLots > 0.0){
    // Check closeable (state 7): held >=4h since last buyable and main down cross + signal>=70 and previous bar main>=signal
    if ( (now - lastEnteredBuyable) >= BUYABLE_MIN_DURATION_SECONDS
         && main_down_cross_0 && sig0 >= 70.0 ){
      if(currentState != STATE_HOLD_BUY_CLOSEABLE){
        currentState = STATE_HOLD_BUY_CLOSEABLE;
        lastStateChange = now;
      }
      return;
    }
    // Check addable (state 3): held >=4h and main up cross + sig<=30
    if ( (now - lastEnteredBuyable) >= BUYABLE_MIN_DURATION_SECONDS
         && main_up_cross_0 && sig0 <= 30.0 ){
      if(currentState != STATE_HOLD_CAN_BUY){
        currentState = STATE_HOLD_CAN_BUY;
        lastStateChange = now;
      }
      return;
    }
    // Waiting buy-hold state (5): Main<20且上穿Signal 则继续为可买状态; Signal ∈ (30,70) 则为持有
    if ( main0 < 20.0 && main_up_cross_0 ){
      if(currentState != STATE_HOLD_BUY_WAIT){
        currentState = STATE_HOLD_BUY_WAIT;
        lastStateChange = now;
      }
      return;
    }
    // else keep current (don't force)
    return;
  }

  // If we hold sells
  double sellLots = TotalLots(false);
  if(sellLots > 0.0){
    // Check closeable (state 8): held >=4h and main up cross + sig<=30 and previous main<=signal
    if ( (now - lastEnteredSellable) >= SELLABLE_MIN_DURATION_SECONDS
         && main_up_cross_0 && sig0 <= 30.0 ){
      if(currentState != STATE_HOLD_SELL_CLOSEABLE){
        currentState = STATE_HOLD_SELL_CLOSEABLE;
        lastStateChange = now;
      }
      return;
    }
    // Check addable (state 4): held >=4h and main down cross + sig>=70
    if ( (now - lastEnteredSellable) >= SELLABLE_MIN_DURATION_SECONDS
         && main_down_cross_0 && sig0 >= 70.0 ){
      if(currentState != STATE_HOLD_CAN_SELL){
        currentState = STATE_HOLD_CAN_SELL;
        lastStateChange = now;
      }
      return;
    }
    // Waiting sell-hold state (6): Main>80且下穿Signal 则继续为卖出持有; Signal ∈ (30,70) 则为持有
    if ( main0 > 80.0 && main_down_cross_0 ){
      if(currentState != STATE_HOLD_SELL_WAIT){
        currentState = STATE_HOLD_SELL_WAIT;
        lastStateChange = now;
      }
      return;
    }
    return;
  }
}

//+------------------------------------------------------------------+
//| Check M5 MA signals and perform trades according to state        |
//+------------------------------------------------------------------+
void CheckMAAndTrade()
{
  // Get necessary MAs at shifts: current bar (0) and previous (1). In M5 timeframe.
  double e0 = MA_e(0), b0 = MA_b(0), d0 = MA_d(0), a0 = MA_a(0);
  double e1 = MA_e(1), b1 = MA_b(1), d1 = MA_d(1), a1 = MA_a(1);

  bool e_cross_b_up = (e0 > b0) && (e1 <= b1);
  bool d_cross_a_down = (d0 < a0) && (d1 >= a1);
  bool e_cross_b_down = (e0 < b0) && (e1 >= b1);
  bool d_cross_a_up = (d0 > a0) && (d1 <= a1);

  // 1. 空仓买入 (state 1)
  if(currentState == STATE_EMPTY_CAN_BUY && e_cross_b_up){
    // open buy 0.1
    OpenBuy(LotsPerTrade);
    // track buyable time (enter hold state)
    lastEnteredBuyable = TimeCurrent();
    currentState = STATE_HOLD_CAN_BUY; // after buy, we go to hold-can-buy state
    lastStateChange = TimeCurrent();
    return;
  }

  // 2. 空仓卖出 (state 2)
  if(currentState == STATE_EMPTY_CAN_SELL && d_cross_a_down){
    OpenSell(LotsPerTrade);
    lastEnteredSellable = TimeCurrent();
    currentState = STATE_HOLD_CAN_SELL;
    lastStateChange = TimeCurrent();
    return;
  }

  // 3. 加仓多单 (state 3)
  if(currentState == STATE_HOLD_CAN_BUY && e_cross_b_up){
    OpenBuy(LotsPerTrade);
    // remain in same state
    return;
  }

  // 4. 加仓空单 (state 4)
  if(currentState == STATE_HOLD_CAN_SELL && d_cross_a_down){
    OpenSell(LotsPerTrade);
    return;
  }

  // 5. 平多单 (state 7)
  if(currentState == STATE_HOLD_BUY_CLOSEABLE && e_cross_b_down){
    CloseBuy(LotsPerTrade); // close 0.1 or all if <=0.1
    // after closing, if positions remain decide state:
    if(TotalLots(true) <= 0.000001){
      currentState = STATE_WAIT_EMPTY;
      lastStateChange = TimeCurrent();
    }
    return;
  }

  // 6. 平空单 (state 8)
  if(currentState == STATE_HOLD_SELL_CLOSEABLE && d_cross_a_up){
    CloseSell(LotsPerTrade);
    if(TotalLots(false) <= 0.000001){
      currentState = STATE_WAIT_EMPTY;
      lastStateChange = TimeCurrent();
    }
    return;
  }
}

//+------------------------------------------------------------------+
//| Order operations: open buy/sell, close buy/sell                  |
//+------------------------------------------------------------------+
void OpenBuy(double lots)
{
  double price = NormalizeDouble(MarketInfo(Symbol(), MODE_ASK), Digits);
  int ticket = OrderSend(Symbol(), OP_BUY, lots, price, Slippage, 0, 0, "EA Buy", MagicNumber, 0, clrGreen);
  if(ticket < 0){
    PrintFormat("OpenBuy failed: %d (err %d)", ticket, GetLastError());
    // optionally handle retries
  } else {
    PrintFormat("Opened BUY ticket=%d lots=%.2f", ticket, lots);
  }
}

void OpenSell(double lots)
{
  double price = NormalizeDouble(MarketInfo(Symbol(), MODE_BID), Digits);
  int ticket = OrderSend(Symbol(), OP_SELL, lots, price, Slippage, 0, 0, "EA Sell", MagicNumber, 0, clrRed);
  if(ticket < 0){
    PrintFormat("OpenSell failed: %d (err %d)", ticket, GetLastError());
  } else {
    PrintFormat("Opened SELL ticket=%d lots=%.2f", ticket, lots);
  }
}

void CloseBuy(double lotsToClose)
{
  // close buy orders until lotsToClose is fulfilled
  double remaining = lotsToClose;
  for(int i=OrdersTotal()-1;i>=0 && remaining>0.000001;i--){
    if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES)){
      if(OrderMagicNumber() == MagicNumber && OrderSymbol() == Symbol() && OrderType() == OP_BUY){
        double closeLots = MathMin(OrderLots(), remaining);
        double price = NormalizeDouble(MarketInfo(Symbol(), MODE_BID), Digits);
        bool ok = OrderClose(OrderTicket(), closeLots, price, Slippage, clrYellow);
        if(!ok){
          PrintFormat("OrderClose(buy) failed ticket=%d err=%d", OrderTicket(), GetLastError());
        } else {
          PrintFormat("Closed BUY ticket=%d lots=%.2f", OrderTicket(), closeLots);
        }
        remaining -= closeLots;
      }
    }
  }
}

void CloseSell(double lotsToClose)
{
  double remaining = lotsToClose;
  for(int i=OrdersTotal()-1;i>=0 && remaining>0.000001;i--){
    if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES)){
      if(OrderMagicNumber() == MagicNumber && OrderSymbol() == Symbol() && OrderType() == OP_SELL){
        double closeLots = MathMin(OrderLots(), remaining);
        double price = NormalizeDouble(MarketInfo(Symbol(), MODE_ASK), Digits);
        bool ok = OrderClose(OrderTicket(), closeLots, price, Slippage, clrYellow);
        if(!ok){
          PrintFormat("OrderClose(sell) failed ticket=%d err=%d", OrderTicket(), GetLastError());
        } else {
          PrintFormat("Closed SELL ticket=%d lots=%.2f", OrderTicket(), closeLots);
        }
        remaining -= closeLots;
      }
    }
  }
}

//+------------------------------------------------------------------+
//| Main tick                                                          |
//+------------------------------------------------------------------+
void OnTick()
{
  // 1. Update trend state based on H4 Stochastic
  UpdateTrendState();

  // 2. Check M5 MA signals and act according to state
  CheckMAAndTrade();

  // 3. Minor housekeeping: if state indicates buyable/sellable set timestamps
  if(currentState == STATE_EMPTY_CAN_BUY || currentState == STATE_HOLD_CAN_BUY){
    if(lastEnteredBuyable==0) lastEnteredBuyable = TimeCurrent();
  }
  if(currentState == STATE_EMPTY_CAN_SELL || currentState == STATE_HOLD_CAN_SELL){
    if(lastEnteredSellable==0) lastEnteredSellable = TimeCurrent();
  }

  // (Optional) Print state for debugging
  static int tickCounter = 0;
  tickCounter++;
  if(tickCounter % 60 == 0) {
    PrintFormat("State=%d buyLots=%.2f sellLots=%.2f", currentState, TotalLots(true), TotalLots(false));
  }
}

//+------------------------------------------------------------------+
