//+------------------------------------------------------------------+
//|  EA: Stochastic(H4) trend + MAs(M5) state-machine trading       |
//|  Full version: SL/TP, per-order retry, trailing stop, limits    |
//|  Language: MQL4                                                 |
//|  Author: ChatGPT (expanded full version, test before live)      |
//+------------------------------------------------------------------+
#property strict

// ---------------------------- INPUTS --------------------------------
input double LotsPerTrade = 0.1;         // 每次开/加仓手数
input double MaxTotalLots = 0.3;         // 最大同时持仓手数（所有方向分别限制）
input int Slippage = 3;
input int MagicNumber = 20250808;
input int MA6_period = 6;
input int MA2_period = 2;

// Stochastic params (H4)
input int Stoch_Kperiod = 20;
input int Stoch_Dperiod = 2;
input int Stoch_Slowing = 2;
input int Stoch_ma_method = MODE_SMA; // MODE_SMA
input int Stoch_price_field = 0;      // 0 = PRICE_CLOSE in iStochastic usage below

// Risk / SL-TP (in pips)
input int StopLossPips = 200;          // 止损（点数/pips）
input int TakeProfitPips = 400;        // 止盈（点数/pips）
input bool UseTrailingStop = true;     // 是否使用移动止损
input int TrailingStopPips = 100;      // 移动止损触发距离（pips）并以此设置追踪SL

// Retry settings for each order operation
input int MaxSendRetries = 3;          // 下单最大重试次数
input int MaxCloseRetries = 3;         // 平仓最大重试次数
input int RetryDelayMs = 500;          // 每次重试间隔（毫秒） — 请不要设置过大，否则影响OnTick响应

// Add/position limits
input int MaxAdds = 2;                 // 每个方向最大加仓次数（不含初始开仓）
input double MinLotsStep = 0.01;       // 最小手数步进（由经纪商决定）

// Time thresholds
#define BUYABLE_MIN_DURATION_SECONDS  (4*3600)   // 4 hours
#define SELLABLE_MIN_DURATION_SECONDS (4*3600)

// State definitions
enum StrategyState {
  STATE_WAIT_EMPTY = 0,
  STATE_EMPTY_CAN_BUY = 1,
  STATE_EMPTY_CAN_SELL = 2,
  STATE_HOLD_CAN_BUY = 3,
  STATE_HOLD_CAN_SELL = 4,
  STATE_HOLD_BUY_WAIT = 5,
  STATE_HOLD_SELL_WAIT = 6,
  STATE_HOLD_BUY_CLOSEABLE = 7,
  STATE_HOLD_SELL_CLOSEABLE = 8
};

// ---------------------------- GLOBALS --------------------------------
int currentState = STATE_WAIT_EMPTY;
datetime lastStateChange = 0;
datetime lastEnteredBuyable = 0;   // when entered state 1 or 3
datetime lastEnteredSellable = 0;  // when entered state 2 or 4

int addsCountBuy = 0;              // 当前多头加仓次数（不含初始）
int addsCountSell = 0;             // 当前空头加仓次数

// -------------------------- INIT / DEINIT -----------------------------
int OnInit()
{
  currentState = STATE_WAIT_EMPTY;
  lastStateChange = TimeCurrent();
  addsCountBuy = 0;
  addsCountSell = 0;
  Print("EA initialized.");
  return(INIT_SUCCEEDED);
}

void OnDeinit(const int reason)
{
  Print("EA deinitialized, reason=", reason);
}

// ----------------------- INDICATOR HELPERS ----------------------------
double StochMain(int shift)
{
  return iStochastic(NULL, PERIOD_H4,
                     Stoch_Kperiod, Stoch_Dperiod, Stoch_Slowing,
                     Stoch_ma_method, Stoch_price_field,
                     MODE_MAIN, shift);
}
double StochSignal(int shift)
{
  return iStochastic(NULL, PERIOD_H4,
                     Stoch_Kperiod, Stoch_Dperiod, Stoch_Slowing,
                     Stoch_ma_method, Stoch_price_field,
                     MODE_SIGNAL, shift);
}

// MAs on M5
double MA_value(int period, int ma_method, int applied_price, int shift)
{
  return iMA(NULL, PERIOD_M5, period, 0, ma_method, applied_price, shift);
}
double MA_a(int shift){ return MA_value(MA6_period, MODE_SMA, PRICE_HIGH, shift); }   // MA_a (6, high)
double MA_b(int shift){ return MA_value(MA6_period, MODE_SMA, PRICE_LOW, shift); }    // MA_b (6, low)
double MA_c(int shift){ return MA_value(MA6_period, MODE_SMA, PRICE_MEDIAN, shift); } // MA_c (6, median)
double MA_d(int shift){ return MA_value(MA2_period, MODE_SMA, PRICE_HIGH, shift); }   // MA_d (2, high)
double MA_e(int shift){ return MA_value(MA2_period, MODE_SMA, PRICE_LOW, shift); }    // MA_e (2, low)
double MA_f(int shift){ return MA_value(MA2_period, MODE_SMA, PRICE_MEDIAN, shift); } // MA_f (2, median)

// ------------------------- POSITION HELPERS ---------------------------
int CountMyOrders()
{
  int cnt = 0;
  for(int i=0;i<OrdersTotal();i++){
    if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES)){
      if(OrderMagicNumber() == MagicNumber && OrderSymbol() == Symbol()) cnt++;
    }
  }
  return cnt;
}

double TotalLotsBuy()
{
  double s = 0.0;
  for(int i=0;i<OrdersTotal();i++){
    if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES)){
      if(OrderMagicNumber() == MagicNumber && OrderSymbol() == Symbol() && OrderType()==OP_BUY) s += OrderLots();
    }
  }
  return s;
}

double TotalLotsSell()
{
  double s = 0.0;
  for(int i=0;i<OrdersTotal();i++){
    if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES)){
      if(OrderMagicNumber() == MagicNumber && OrderSymbol() == Symbol() && OrderType()==OP_SELL) s += OrderLots();
    }
  }
  return s;
}

int CountBuyOrders()
{
  int n=0;
  for(int i=0;i<OrdersTotal();i++){
    if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES)){
      if(OrderMagicNumber() == MagicNumber && OrderSymbol() == Symbol() && OrderType()==OP_BUY) n++;
    }
  }
  return n;
}
int CountSellOrders()
{
  int n=0;
  for(int i=0;i<OrdersTotal();i++){
    if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES)){
      if(OrderMagicNumber() == MagicNumber && OrderSymbol() == Symbol() && OrderType()==OP_SELL) n++;
    }
  }
  return n;
}

// --------------------- PRICE & SL/TP CALC ------------------------------
double PointToPrice(int pips, bool isBuy)
{
  // pips -> price (respecting Digits)
  double p = pips * Point;
  if(Digits==3 || Digits==5) p = pips * Point * 10.0; // 5-digit brokers: 1 pip = 10 points
  return p;
}

double PriceForSL(bool isBuy)
{
  // For Buy: SL = Bid - SLpips
  // For Sell: SL = Ask + SLpips
  double pips = StopLossPips;
  double delta = PointToPrice(pips, isBuy);
  if(isBuy) return NormalizeDouble(Bid - delta, Digits);
  else return NormalizeDouble(Ask + delta, Digits);
}

double PriceForTP(bool isBuy)
{
  double pips = TakeProfitPips;
  double delta = PointToPrice(pips, isBuy);
  if(isBuy) return NormalizeDouble(Bid + delta, Digits);
  else return NormalizeDouble(Ask - delta, Digits);
}

// -------------------- SAFE ORDER FUNCTIONS (重试) ----------------------
int SafeOrderSend(int type, double lots, double price, double sl, double tp, string comment="")
{
  // Returns ticket or -1 on failure
  int tries = 0;
  while(tries < MaxSendRetries){
    RefreshRates();
    int ticket = OrderSend(Symbol(), type, NormalizeLot(lots), price, Slippage, sl, tp, comment, MagicNumber, 0, clrNONE);
    if(ticket > 0){
      PrintFormat("OrderSend success: ticket=%d type=%d lots=%.2f price=%.5f SL=%.5f TP=%.5f", ticket, type, lots, price, sl, tp);
      return ticket;
    } else {
      int err = GetLastError();
      PrintFormat("OrderSend failed (attempt %d/%d): err=%d", tries+1, MaxSendRetries, err);
      // Clear certain errors if needed
      Sleep(RetryDelayMs);
      tries++;
      // try to reset last error
      ResetLastError();
    }
  }
  Print("OrderSend ultimately failed after retries.");
  return -1;
}

bool SafeOrderClose(int ticket, double lots)
{
  int tries = 0;
  while(tries < MaxCloseRetries){
    RefreshRates();
    if(!OrderSelect(ticket, SELECT_BY_TICKET)) {
      PrintFormat("SafeOrderClose: cannot select ticket %d", ticket);
      return false;
    }
    int type = OrderType();
    double price = (type==OP_BUY) ? Bid : Ask;
    bool ok = OrderClose(ticket, NormalizeLot(lots), price, Slippage, clrNONE);
    if(ok){
      PrintFormat("OrderClose success ticket=%d lots=%.2f", ticket, lots);
      return true;
    } else {
      int err = GetLastError();
      PrintFormat("OrderClose failed (attempt %d/%d) ticket=%d err=%d", tries+1, MaxCloseRetries, ticket, err);
      Sleep(RetryDelayMs);
      tries++;
      ResetLastError();
    }
  }
  PrintFormat("SafeOrderClose ultimately failed for ticket %d", ticket);
  return false;
}

// Normalize lot to broker steps and min lot
double NormalizeLot(double lots)
{
  double step = MarketInfo(Symbol(), MODE_LOTSTEP);
  double minlot = MarketInfo(Symbol(), MODE_MINLOT);
  double maxlot = MarketInfo(Symbol(), MODE_MAXLOT);
  if(step <= 0) step = MinLotStepFallback();
  double res = MathFloor(lots/step + 0.0000001) * step;
  if(res < minlot) res = minlot;
  if(res > maxlot) res = maxlot;
  res = NormalizeDouble(res, 2);
  return res;
}
double MinLotStepFallback()
{
  if(MinLotsStep > 0) return MinLotsStep;
  return 0.01;
}

// ----------------------- TRADING WRAPPERS ------------------------------
void OpenBuy(double lots)
{
  // Check max total buy lots vs MaxTotalLots
  double currentBuy = TotalLotsBuy();
  if(currentBuy + lots > MaxTotalLots + 0.000001){
    Print("OpenBuy blocked: exceed MaxTotalLots for buy. curr=", DoubleToString(currentBuy,2));
    return;
  }
  double price = NormalizeDouble(Ask, Digits);
  double sl = PriceForSL(true);
  double tp = PriceForTP(true);
  int ticket = SafeOrderSend(OP_BUY, lots, price, sl, tp, "EA Buy");
  if(ticket > 0) {
    // reset adds if new direction started or increment
    if(currentBuy < 0.000001) addsCountBuy = 0; else addsCountBuy++;
    lastEnteredBuyable = TimeCurrent();
  }
}

void OpenSell(double lots)
{
  double currentSell = TotalLotsSell();
  if(currentSell + lots > MaxTotalLots + 0.000001){
    Print("OpenSell blocked: exceed MaxTotalLots for sell. curr=", DoubleToString(currentSell,2));
    return;
  }
  double price = NormalizeDouble(Bid, Digits);
  double sl = PriceForSL(false);
  double tp = PriceForTP(false);
  int ticket = SafeOrderSend(OP_SELL, lots, price, sl, tp, "EA Sell");
  if(ticket > 0){
    if(currentSell < 0.000001) addsCountSell = 0; else addsCountSell++;
    lastEnteredSellable = TimeCurrent();
  }
}

void CloseBuy(double lotsToClose)
{
  // iterate buy orders, close until lotsToClose fulfilled
  double remaining = lotsToClose;
  for(int i=OrdersTotal()-1;i>=0 && remaining>0.000001;i--){
    if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES)){
      if(OrderMagicNumber()==MagicNumber && OrderSymbol()==Symbol() && OrderType()==OP_BUY){
        double closeLots = MathMin(OrderLots(), remaining);
        bool ok = SafeOrderClose(OrderTicket(), closeLots);
        if(ok) remaining -= closeLots;
        else { Print("CloseBuy: failed to close some lots"); break; }
      }
    }
  }
}

void CloseSell(double lotsToClose)
{
  double remaining = lotsToClose;
  for(int i=OrdersTotal()-1;i>=0 && remaining>0.000001;i--){
    if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES)){
      if(OrderMagicNumber()==MagicNumber && OrderSymbol()==Symbol() && OrderType()==OP_SELL){
        double closeLots = MathMin(OrderLots(), remaining);
        bool ok = SafeOrderClose(OrderTicket(), closeLots);
        if(ok) remaining -= closeLots;
        else { Print("CloseSell: failed to close some lots"); break; }
      }
    }
  }
}

// ------------------ UPDATE TREND STATE (H4 Stochastic) ----------------
void UpdateTrendState()
{
  double main0 = StochMain(0);
  double sig0  = StochSignal(0);
  double main1 = StochMain(1);
  double sig1  = StochSignal(1);

  datetime now = TimeCurrent();
  bool main_up_cross_0 = (main0 > sig0) && (main1 <= sig1);
  bool main_down_cross_0 = (main0 < sig0) && (main1 >= sig1);

  double totalLots = TotalLotsBuy() + TotalLotsSell();

  if(totalLots < 0.000001) {
    // empty
    if ( main_up_cross_0 && sig0 <= 30.0 ) {
      if(currentState != STATE_EMPTY_CAN_BUY){
        currentState = STATE_EMPTY_CAN_BUY;
        lastStateChange = now;
        lastEnteredBuyable = now;
        Print("State -> EMPTY_CAN_BUY");
      }
    } else if ( main_down_cross_0 && sig0 >= 70.0 ) {
      if(currentState != STATE_EMPTY_CAN_SELL){
        currentState = STATE_EMPTY_CAN_SELL;
        lastStateChange = now;
        lastEnteredSellable = now;
        Print("State -> EMPTY_CAN_SELL");
      }
    } else {
      if(currentState != STATE_WAIT_EMPTY){
        currentState = STATE_WAIT_EMPTY;
        lastStateChange = now;
        Print("State -> WAIT_EMPTY");
      }
    }
    return;
  }

  // If hold buys
  double buyLots = TotalLotsBuy();
  if(buyLots > 0.0){
    // closeable
    if ( (now - lastEnteredBuyable) >= BUYABLE_MIN_DURATION_SECONDS
         && main_down_cross_0 && sig0 >= 70.0 ){
      if(currentState != STATE_HOLD_BUY_CLOSEABLE){
        currentState = STATE_HOLD_BUY_CLOSEABLE;
        lastStateChange = now;
        Print("State -> HOLD_BUY_CLOSEABLE");
      }
      return;
    }
    // addable
    if ( (now - lastEnteredBuyable) >= BUYABLE_MIN_DURATION_SECONDS
         && main_up_cross_0 && sig0 <= 30.0 ){
      if(currentState != STATE_HOLD_CAN_BUY){
        currentState = STATE_HOLD_CAN_BUY;
        lastStateChange = now;
        Print("State -> HOLD_CAN_BUY");
      }
      return;
    }
    // waiting buy-hold
    if ( main0 < 20.0 && main_up_cross_0 ){
      if(currentState != STATE_HOLD_BUY_WAIT){
        currentState = STATE_HOLD_BUY_WAIT;
        lastStateChange = now;
        Print("State -> HOLD_BUY_WAIT");
      }
      return;
    }
    return;
  }

  // If hold sells
  double sellLots = TotalLotsSell();
  if(sellLots > 0.0){
    if ( (now - lastEnteredSellable) >= SELLABLE_MIN_DURATION_SECONDS
         && main_up_cross_0 && sig0 <= 30.0 ){
      if(currentState != STATE_HOLD_SELL_CLOSEABLE){
        currentState = STATE_HOLD_SELL_CLOSEABLE;
        lastStateChange = now;
        Print("State -> HOLD_SELL_CLOSEABLE");
      }
      return;
    }
    if ( (now - lastEnteredSellable) >= SELLABLE_MIN_DURATION_SECONDS
         && main_down_cross_0 && sig0 >= 70.0 ){
      if(currentState != STATE_HOLD_CAN_SELL){
        currentState = STATE_HOLD_CAN_SELL;
        lastStateChange = now;
        Print("State -> HOLD_CAN_SELL");
      }
      return;
    }
    if ( main0 > 80.0 && main_down_cross_0 ){
      if(currentState != STATE_HOLD_SELL_WAIT){
        currentState = STATE_HOLD_SELL_WAIT;
        lastStateChange = now;
        Print("State -> HOLD_SELL_WAIT");
      }
      return;
    }
    return;
  }
}

// ------------------- M5 MA SIGNALS & TRADING LOGIC --------------------
void CheckMAAndTrade()
{
  double e0 = MA_e(0), b0 = MA_b(0), d0 = MA_d(0), a0 = MA_a(0);
  double e1 = MA_e(1), b1 = MA_b(1), d1 = MA_d(1), a1 = MA_a(1);

  bool e_cross_b_up = (e0 > b0) && (e1 <= b1);
  bool d_cross_a_down = (d0 < a0) && (d1 >= a1);
  bool e_cross_b_down = (e0 < b0) && (e1 >= b1);
  bool d_cross_a_up = (d0 > a0) && (d1 <= a1);

  // 1. 空仓买入 (state 1)
  if(currentState == STATE_EMPTY_CAN_BUY && e_cross_b_up){
    // open buy 0.1
    OpenBuy(LotsPerTrade);
    lastEnteredBuyable = TimeCurrent();
    // after opening, set hold state
    currentState = STATE_HOLD_CAN_BUY;
    lastStateChange = TimeCurrent();
    return;
  }

  // 2. 空仓卖出 (state 2)
  if(currentState == STATE_EMPTY_CAN_SELL && d_cross_a_down){
    OpenSell(LotsPerTrade);
    lastEnteredSellable = TimeCurrent();
    currentState = STATE_HOLD_CAN_SELL;
    lastStateChange = TimeCurrent();
    return;
  }

  // 3. 加仓多单 (state 3)
  if(currentState == STATE_HOLD_CAN_BUY && e_cross_b_up){
    if(addsCountBuy < MaxAdds){
      OpenBuy(LotsPerTrade);
      addsCountBuy++;
    } else {
      Print("Add buy blocked: reached MaxAdds");
    }
    return;
  }

  // 4. 加仓空单 (state 4)
  if(currentState == STATE_HOLD_CAN_SELL && d_cross_a_down){
    if(addsCountSell < MaxAdds){
      OpenSell(LotsPerTrade);
      addsCountSell++;
    } else {
      Print("Add sell blocked: reached MaxAdds");
    }
    return;
  }

  // 5. 平多单 (state 7)
  if(currentState == STATE_HOLD_BUY_CLOSEABLE && e_cross_b_down){
    CloseBuy(LotsPerTrade);
    if(TotalLotsBuy() <= 0.000001){
      currentState = STATE_WAIT_EMPTY;
      lastStateChange = TimeCurrent();
      addsCountBuy = 0;
    }
    return;
  }

  // 6. 平空单 (state 8)
  if(currentState == STATE_HOLD_SELL_CLOSEABLE && d_cross_a_up){
    CloseSell(LotsPerTrade);
    if(TotalLotsSell() <= 0.000001){
      currentState = STATE_WAIT_EMPTY;
      lastStateChange = TimeCurrent();
      addsCountSell = 0;
    }
    return;
  }
}

// ---------------------- TRAILING STOP LOGIC ---------------------------
void ManageTrailingStops()
{
  if(!UseTrailingStop) return;
  // For each order of this EA adjust SL if price moved favorably beyond TrailingStopPips
  for(int i=0;i<OrdersTotal();i++){
    if(!OrderSelect(i, SELECT_BY_POS, MODE_TRADES)) continue;
    if(OrderMagicNumber()!=MagicNumber || OrderSymbol()!=Symbol()) continue;

    int type = OrderType();
    double orderOpenPrice = OrderOpenPrice();
    double currentSL = OrderStopLoss();
    double targetSL;

    if(type == OP_BUY){
      double favorablePrice = Bid - orderOpenPrice;
      double trigger = PointToPrice(TrailingStopPips, true);
      if((Bid - orderOpenPrice) >= trigger){
        // move SL up to currentBid - TrailingStopPips
        targetSL = NormalizeDouble(Bid - PointToPrice(TrailingStopPips, true), Digits);
        if(targetSL > currentSL + Point/2){
          // modify order
          bool mod = OrderModify(OrderTicket(), orderOpenPrice, targetSL, OrderTakeProfit(), 0, clrYellow);
          if(mod) PrintFormat("Trailing SL updated for ticket %d to %.5f", OrderTicket(), targetSL);
          else PrintFormat("Trailing SL modify failed ticket=%d err=%d", OrderTicket(), GetLastError());
        }
      }
    } else if(type == OP_SELL){
      double trigger = PointToPrice(TrailingStopPips, false);
      if((orderOpenPrice - Ask) >= trigger){
        targetSL = NormalizeDouble(Ask + PointToPrice(TrailingStopPips, false), Digits);
        if(targetSL < currentSL - Point/2 || currentSL==0.0){
          bool mod = OrderModify(OrderTicket(), orderOpenPrice, targetSL, OrderTakeProfit(), 0, clrYellow);
          if(mod) PrintFormat("Trailing SL updated for ticket %d to %.5f", OrderTicket(), targetSL);
          else PrintFormat("Trailing SL modify failed ticket=%d err=%d", OrderTicket(), GetLastError());
        }
      }
    }
  }
}

// ------------------------- MAIN TICK ---------------------------------
void OnTick()
{
  // Basic guard: ensure enough bars
  if(Bars < 50) return;

  UpdateTrendState();
  CheckMAAndTrade();
  ManageTrailingStops();

  static int tickCounter = 0;
  tickCounter++;
  if(tickCounter % 300 == 0) { // every 300 ticks print summary
    PrintFormat("Summary: State=%d BuyLots=%.2f SellLots=%.2f AddsBuy=%d AddsSell=%d",
                currentState, TotalLotsBuy(), TotalLotsSell(), addsCountBuy, addsCountSell);
  }
}

//+------------------------------------------------------------------+
