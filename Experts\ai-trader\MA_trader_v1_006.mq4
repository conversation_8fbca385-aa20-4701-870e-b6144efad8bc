//+------------------------------------------------------------------+
//| Full EA: State-machine + H4 Stochastic trend + M5 MA signals     |
//| Smart Trailing (High/Low + MA) + SL/TP + per-order retry         |
//| Single-file, commented, ready for MT4                            |
//| Author: ChatGPT (generated) -- TEST BEFORE LIVE                   |
//+------------------------------------------------------------------+
#property strict

//------------------------- INPUT PARAMETERS --------------------------
input double LotsPerTrade     = 0.1;     // 每次开/加仓手数
input double MaxTotalLots     = 0.3;     // 单方向最大持仓手数（可根据需要修改）
input int    Slippage         = 3;       // 最大滑点
input int    MagicNumber      = 20250808;// EA 魔术号

// MA 参数 (M5)
input int MA6_period = 6;
input int MA2_period = 2;

// Stochastic 参数 (H4)
input int Stoch_Kperiod = 20;
input int Stoch_Dperiod = 2;
input int Stoch_Slowing = 2;
input int Stoch_ma_method = MODE_SMA;
input int Stoch_price_field = 0; // PRICE_CLOSE

// 固定止损/止盈（pips）
input int StopLossPips    = 30;
input int TakeProfitPips  = 60;

// 智能 Trailing 参数
input int  TrailingLookbackBars = 5;   // 参考最近 N 根 M5 K线的高低
input int  TrailingBufferPips   = 5;   // 将 MA 与高低点比较时的缓冲（pips）
input int  TrailingActivatePips = 20;  // 浮盈达到多少 pips 开始追踪

// 重试/保护参数
input int MaxSendRetries   = 3;    // 下单最大重试次数
input int MaxCloseRetries  = 3;    // 平仓最大重试次数
input int RetryDelayMs     = 500;  // 每次重试间隔（毫秒）
input int MaxAdds          = 2;    // 最大加仓次数（不含初始开仓）

// 时间阈值（秒）
#define HOLD_MIN_SECONDS  (4*3600)  // 4小时最小持仓时间门槛，用于判断持仓是否满足“超过4小时”的条件

//------------------------- 全局变量 ----------------------------------
int currentState = 0;               // 0-8 状态机
datetime lastEnteredBuyable = 0;
datetime lastEnteredSellable = 0;
int addsCountBuy = 0;
int addsCountSell = 0;

//------------------------- 帮助函数 ----------------------------------
// 将用户设定的 pips 转换为价格差值（考虑 4/5 位经纪商）
double PipsToPrice(double pips) {
  if(Digits==3 || Digits==5) return pips * Point * 10.0;
  return pips * Point;
}

// 规范化手数到经纪商允许的步进
double NormalizeLot(double lots) {
  double step = MarketInfo(Symbol(), MODE_LOTSTEP);
  double minlot = MarketInfo(Symbol(), MODE_MINLOT);
  double maxlot = MarketInfo(Symbol(), MODE_MAXLOT);
  if(step <= 0) step = 0.01;
  double res = MathFloor(lots/step + 0.0000001) * step;
  if(res < minlot) res = minlot;
  if(res > maxlot) res = maxlot;
  return NormalizeDouble(res, 2);
}

// 计算当前账户下 EA 的多头手数
double TotalLotsBuy() {
  double s = 0.0;
  for(int i=0;i<OrdersTotal();i++){
    if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES)){
      if(OrderMagicNumber()==MagicNumber && OrderSymbol()==Symbol() && OrderType()==OP_BUY) s += OrderLots();
    }
  }
  return s;
}

// 计算当前账户下 EA 的空头手数
double TotalLotsSell() {
  double s = 0.0;
  for(int i=0;i<OrdersTotal();i++){
    if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES)){
      if(OrderMagicNumber()==MagicNumber && OrderSymbol()==Symbol() && OrderType()==OP_SELL) s += OrderLots();
    }
  }
  return s;
}

// 统计当前魔术号订单数量
int OrdersTotalByMagic() {
  int cnt = 0;
  for(int i=0;i<OrdersTotal();i++){
    if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES)){
      if(OrderMagicNumber()==MagicNumber && OrderSymbol()==Symbol()) cnt++;
    }
  }
  return cnt;
}

//------------------------- 指标包装函数 -------------------------------
double StochMain(int shift) {
  return iStochastic(NULL, PERIOD_H4, Stoch_Kperiod, Stoch_Dperiod, Stoch_Slowing, Stoch_ma_method, Stoch_price_field, MODE_MAIN, shift);
}
double StochSignal(int shift) {
  return iStochastic(NULL, PERIOD_H4, Stoch_Kperiod, Stoch_Dperiod, Stoch_Slowing, Stoch_ma_method, Stoch_price_field, MODE_SIGNAL, shift);
}

double MA_value(int period, int ma_method, int applied_price, int shift) {
  return iMA(NULL, PERIOD_M5, period, 0, ma_method, applied_price, shift);
}
double MA_a(int shift){ return MA_value(MA6_period, MODE_SMA, PRICE_HIGH, shift); }   // MA_a: MA6 HIGH
double MA_b(int shift){ return MA_value(MA6_period, MODE_SMA, PRICE_LOW, shift); }    // MA_b: MA6 LOW
double MA_c(int shift){ return MA_value(MA6_period, MODE_SMA, PRICE_MEDIAN, shift); } // MA_c: MA6 MEDIAN
double MA_d(int shift){ return MA_value(MA2_period, MODE_SMA, PRICE_HIGH, shift); }   // MA_d: MA2 HIGH
double MA_e(int shift){ return MA_value(MA2_period, MODE_SMA, PRICE_LOW, shift); }    // MA_e: MA2 LOW
double MA_f(int shift){ return MA_value(MA2_period, MODE_SMA, PRICE_MEDIAN, shift); } // MA_f: MA2 MEDIAN

//------------------------- Order helpers (带重试) ----------------------
int SafeOrderSend(int type, double lots, double price, double sl, double tp, string comment="EA Order") {
  int tries = 0;
  int ticket = -1;
  while(tries < MaxSendRetries) {
    RefreshRates();
    ticket = OrderSend(Symbol(), type, NormalizeLot(lots), price, Slippage, sl, tp, comment, MagicNumber, 0, clrNONE);
    if(ticket > 0) {
      PrintFormat("OrderSend success ticket=%d type=%d lots=%.2f price=%.5f SL=%.5f TP=%.5f", ticket, type, lots, price, sl, tp);
      return ticket;
    } else {
      int err = GetLastError();
      PrintFormat("OrderSend failed (attempt %d/%d): err=%d", tries+1, MaxSendRetries, err);
      ResetLastError();
      Sleep(RetryDelayMs);
      tries++;
    }
  }
  Print("OrderSend ultimately failed after retries.");
  return -1;
}

bool SafeOrderClose(int ticket, double lots) {
  int tries = 0;
  while(tries < MaxCloseRetries) {
    RefreshRates();
    if(!OrderSelect(ticket, SELECT_BY_TICKET)) {
      PrintFormat("SafeOrderClose: cannot select ticket %d", ticket);
      return false;
    }
    int type = OrderType();
    double price = (type==OP_BUY) ? Bid : Ask;
    if(OrderClose(ticket, NormalizeLot(lots), price, Slippage, clrNONE)) {
      PrintFormat("OrderClose success ticket=%d lots=%.2f", ticket, lots);
      return true;
    } else {
      int err = GetLastError();
      PrintFormat("OrderClose failed (attempt %d/%d) ticket=%d err=%d", tries+1, MaxCloseRetries, ticket, err);
      ResetLastError();
      Sleep(RetryDelayMs);
      tries++;
    }
  }
  PrintFormat("SafeOrderClose ultimately failed for ticket %d", ticket);
  return false;
}

//------------------------- 下单/平仓封装 --------------------------------
void OpenBuy(double lots) {
  double price = NormalizeDouble(Ask, Digits);
  double sl = NormalizeDouble(price - PipsToPrice(StopLossPips), Digits);
  double tp = NormalizeDouble(price + PipsToPrice(TakeProfitPips), Digits);
  int t = SafeOrderSend(OP_BUY, lots, price, sl, tp, "EA Buy");
  if(t > 0) {
    if(TotalLotsBuy() <= lots + 0.000001) addsCountBuy = 0; else addsCountBuy++;
    lastEnteredBuyable = TimeCurrent();
  }
}

void OpenSell(double lots) {
  double price = NormalizeDouble(Bid, Digits);
  double sl = NormalizeDouble(price + PipsToPrice(StopLossPips), Digits);
  double tp = NormalizeDouble(price - PipsToPrice(TakeProfitPips), Digits);
  int t = SafeOrderSend(OP_SELL, lots, price, sl, tp, "EA Sell");
  if(t > 0) {
    if(TotalLotsSell() <= lots + 0.000001) addsCountSell = 0; else addsCountSell++;
    lastEnteredSellable = TimeCurrent();
  }
}

// Close specified lots of given type (tries across orders)
void CloseBuy(double lotsToClose) {
  double remaining = lotsToClose;
  for(int i=OrdersTotal()-1;i>=0 && remaining>0.000001;i--){
    if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES)){
      if(OrderMagicNumber()==MagicNumber && OrderSymbol()==Symbol() && OrderType()==OP_BUY){
        double closeLots = MathMin(OrderLots(), remaining);
        bool ok = SafeOrderClose(OrderTicket(), closeLots);
        if(ok) remaining -= closeLots;
        else {
          PrintFormat("CloseBuy: failed to close ticket %d", OrderTicket());
          break;
        }
      }
    }
  }
}

void CloseSell(double lotsToClose) {
  double remaining = lotsToClose;
  for(int i=OrdersTotal()-1;i>=0 && remaining>0.000001;i--){
    if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES)){
      if(OrderMagicNumber()==MagicNumber && OrderSymbol()==Symbol() && OrderType()==OP_SELL){
        double closeLots = MathMin(OrderLots(), remaining);
        bool ok = SafeOrderClose(OrderTicket(), closeLots);
        if(ok) remaining -= closeLots;
        else {
          PrintFormat("CloseSell: failed to close ticket %d", OrderTicket());
          break;
        }
      }
    }
  }
}

//------------------------- 状态机（0-8）实现 ----------------------------
void UpdateTrendState() {
  // 获取 H4 Stochastic 当前与上一根柱
  double main0 = StochMain(0);
  double sig0  = StochSignal(0);
  double main1 = StochMain(1);
  double sig1  = StochSignal(1);

  datetime now = TimeCurrent();

  bool main_up_cross_0 = (main0 > sig0) && (main1 <= sig1);    // Bar(0) 上穿且 Bar(1) main <= signal
  bool main_down_cross_0 = (main0 < sig0) && (main1 >= sig1);  // Bar(0) 下穿且 Bar(1) main >= signal

  double totalLots = TotalLotsBuy() + TotalLotsSell();

  // 空仓分支
  if(totalLots < 0.000001) {
    if(main_up_cross_0 && sig0 <= 30.0) {
      if(currentState != 1) {
        currentState = 1; // 空仓可买
        lastEnteredBuyable = now;
        Print("State -> EMPTY_CAN_BUY (1)");
      }
      return;
    }
    if(main_down_cross_0 && sig0 >= 70.0) {
      if(currentState != 2) {
        currentState = 2; // 空仓可卖
        lastEnteredSellable = now;
        Print("State -> EMPTY_CAN_SELL (2)");
      }
      return;
    }
    if(currentState != 0) {
      currentState = 0; // 空仓等待
      Print("State -> WAIT_EMPTY (0)");
    }
    return;
  }

  // 持有多头分支
  double buyLots = TotalLotsBuy();
  if(buyLots > 0.0) {
    // 持买仓可平仓 (7)
    if( (now - lastEnteredBuyable) >= HOLD_MIN_SECONDS && main_down_cross_0 && sig0 >= 70.0 && (main1 >= sig1) ) {
      if(currentState != 7) {
        currentState = 7;
        Print("State -> HOLD_BUY_CLOSEABLE (7)");
      }
      return;
    }
    // 持买仓可买 (3)
    if( (now - lastEnteredBuyable) >= HOLD_MIN_SECONDS && main_up_cross_0 && sig0 <= 30.0 && (main1 <= sig1) ) {
      if(currentState != 3) {
        currentState = 3;
        Print("State -> HOLD_CAN_BUY (3)");
      }
      return;
    }
    // 持买仓等待 (5)
    if(main0 < 20.0 && main_up_cross_0) {
      if(currentState != 5) {
        currentState = 5;
        Print("State -> HOLD_BUY_WAIT (5)");
      }
      return;
    }
    return;
  }

  // 持有空头分支
  double sellLots = TotalLotsSell();
  if(sellLots > 0.0) {
    // 持卖仓可平仓 (8)
    if( (now - lastEnteredSellable) >= HOLD_MIN_SECONDS && main_up_cross_0 && sig0 <= 30.0 && (main1 <= sig1) ) {
      if(currentState != 8) {
        currentState = 8;
        Print("State -> HOLD_SELL_CLOSEABLE (8)");
      }
      return;
    }
    // 持卖仓可卖 (4)
    if( (now - lastEnteredSellable) >= HOLD_MIN_SECONDS && main_down_cross_0 && sig0 >= 70.0 && (main1 >= sig1) ) {
      if(currentState != 4) {
        currentState = 4;
        Print("State -> HOLD_CAN_SELL (4)");
      }
      return;
    }
    // 持卖仓等待 (6)
    if(main0 > 80.0 && main_down_cross_0) {
      if(currentState != 6) {
        currentState = 6;
        Print("State -> HOLD_SELL_WAIT (6)");
      }
      return;
    }
    return;
  }
}

//------------------------- M5 MA 信号 & 交易执行 ------------------------
void CheckMAAndTrade() {
  // M5 current/previous MAs
  double e0 = MA_e(0), b0 = MA_b(0), d0 = MA_d(0), a0 = MA_a(0);
  double e1 = MA_e(1), b1 = MA_b(1), d1 = MA_d(1), a1 = MA_a(1);

  bool e_cross_b_up = (e0 > b0) && (e1 <= b1);
  bool d_cross_a_down = (d0 < a0) && (d1 >= a1);
  bool e_cross_b_down = (e0 < b0) && (e1 >= b1);
  bool d_cross_a_up = (d0 > a0) && (d1 <= a1);

  // 空仓买入 (1)
  if(currentState == 1 && OrdersTotalByMagic() == 0 && e_cross_b_up) {
    if(TotalLotsBuy() + LotsPerTrade <= MaxTotalLots + 0.000001) {
      OpenBuy(LotsPerTrade);
      lastEnteredBuyable = TimeCurrent();
      addsCountBuy = 0;
    }
    return;
  }

  // 空仓卖出 (2)
  if(currentState == 2 && OrdersTotalByMagic() == 0 && d_cross_a_down) {
    if(TotalLotsSell() + LotsPerTrade <= MaxTotalLots + 0.000001) {
      OpenSell(LotsPerTrade);
      lastEnteredSellable = TimeCurrent();
      addsCountSell = 0;
    }
    return;
  }

  // 持买仓可加仓 (3)
  if(currentState == 3 && e_cross_b_up && TotalLotsBuy() > 0.0) {
    if(addsCountBuy < MaxAdds && TotalLotsBuy() + LotsPerTrade <= MaxTotalLots + 0.000001) {
      OpenBuy(LotsPerTrade);
      addsCountBuy++;
    } else {
      // 达到加仓上限，跳过
    }
    return;
  }

  // 持卖仓可加仓 (4)
  if(currentState == 4 && d_cross_a_down && TotalLotsSell() > 0.0) {
    if(addsCountSell < MaxAdds && TotalLotsSell() + LotsPerTrade <= MaxTotalLots + 0.000001) {
      OpenSell(LotsPerTrade);
      addsCountSell++;
    } else {
      // 达到加仓上限
    }
    return;
  }

  // 持买仓可平仓 (7)
  if(currentState == 7 && e_cross_b_down && TotalLotsBuy() > 0.0) {
    CloseBuy(LotsPerTrade);
    if(TotalLotsBuy() <= 0.000001) {
      currentState = 0; // 回到空仓等待
      Print("After closing buys -> WAIT_EMPTY (0)");
    }
    return;
  }

  // 持卖仓可平仓 (8)
  if(currentState == 8 && d_cross_a_up && TotalLotsSell() > 0.0) {
    CloseSell(LotsPerTrade);
    if(TotalLotsSell() <= 0.000001) {
      currentState = 0;
      Print("After closing sells -> WAIT_EMPTY (0)");
    }
    return;
  }
}

//------------------------- 智能 Trailing (高低点 + MA) ------------------
void SmartTrailingStops() {
  for(int i=OrdersTotal()-1; i>=0; i--) {
    if(!OrderSelect(i, SELECT_BY_POS, MODE_TRADES)) continue;
    if(OrderMagicNumber() != MagicNumber || OrderSymbol() != Symbol()) continue;

    int type = OrderType();
    double openPrice = OrderOpenPrice();
    double curSL = OrderStopLoss();
    double curTP = OrderTakeProfit();

    // 计算浮盈 (单位: pips)
    double profitPips = (type == OP_BUY) ? (Bid - openPrice) / PipsToPrice(1) : (openPrice - Ask) / PipsToPrice(1);
    if(profitPips < TrailingActivatePips) continue; // 未达到激活条件

    if(type == OP_BUY) {
      // 最近 N 根 M5 的最低价与 MA6(median)
      int idxLow = iLowest(NULL, PERIOD_M5, MODE_LOW, TrailingLookbackBars, 1);
      double lowestLow = (idxLow >= 0) ? Low[idxLow] : Low[1];
      double maMedian = MA_c(0);
      double buffer = PipsToPrice(TrailingBufferPips);
      double newSL = MathMax(lowestLow, maMedian - buffer);
      // 仅当新的 SL 明显高于当前 SL 时才修改，避免频繁微调
      if(newSL > curSL + Point) {
        int tries = 0;
        bool modOK = false;
        while(tries < MaxSendRetries && !modOK) {
          RefreshRates();
          modOK = OrderModify(OrderTicket(), openPrice, NormalizeDouble(newSL,Digits), curTP, 0, clrYellow);
          if(!modOK) {
            int err = GetLastError();
            PrintFormat("OrderModify(BUY) failed attempt %d err=%d ticket=%d", tries+1, err, OrderTicket());
            ResetLastError();
            Sleep(RetryDelayMs);
          }
          tries++;
        }
        if(modOK) PrintFormat("Updated BUY SL ticket=%d newSL=%.5f", OrderTicket(), newSL);
      }
    }
    else if(type == OP_SELL) {
      int idxHigh = iHighest(NULL, PERIOD_M5, MODE_HIGH, TrailingLookbackBars, 1);
      double highestHigh = (idxHigh >= 0) ? High[idxHigh] : High[1];
      double maMedianFast = MA_f(0); // MA2 median
      double buffer = PipsToPrice(TrailingBufferPips);
      double newSL = MathMin(highestHigh, maMedianFast + buffer);
      // 仅当新的 SL 明显低于当前 SL 时才修改
      if(curSL == 0.0 || newSL < curSL - Point) {
        int tries = 0;
        bool modOK = false;
        while(tries < MaxSendRetries && !modOK) {
          RefreshRates();
          modOK = OrderModify(OrderTicket(), openPrice, NormalizeDouble(newSL,Digits), curTP, 0, clrYellow);
          if(!modOK) {
            int err = GetLastError();
            PrintFormat("OrderModify(SELL) failed attempt %d err=%d ticket=%d", tries+1, err, OrderTicket());
            ResetLastError();
            Sleep(RetryDelayMs);
          }
          tries++;
        }
        if(modOK) PrintFormat("Updated SELL SL ticket=%d newSL=%.5f", OrderTicket(), newSL);
      }
    }
  }
}

//------------------------- OnInit / OnTick 简短日志 ----------------------
int OnInit() {
  Print("EA loaded: State-machine + SmartTrailing. Magic=", MagicNumber);
  return(INIT_SUCCEEDED);
}

void OnDeinit(const int reason) {
  Print("EA deinitialized, reason=", reason);
}

void OnTick() {
  // 少许保护
  if(Bars < 200) return;

  UpdateTrendState();
  CheckMAAndTrade(); // 这里改名以兼容之前定义
  SmartTrailingStops();

  static int cnt=0; cnt++;
  if(cnt % 300 == 0) {
    PrintFormat("State=%d BuyLots=%.2f SellLots=%.2f AddsBuy=%d AddsSell=%d",
                currentState, TotalLotsBuy(), TotalLotsSell(), addsCountBuy, addsCountSell);
  }
}

//------------------------- 兼容性：为防止重定义 ------------------------
// (上述有些函数如 OnInit/OnTick 已定义两次，下面是避免编译重复：如果你的编辑器提示重复定义，
// 请保留上面一组 OnInit/OnTick 并删除下面重复的空实现。)
//--------------------------------------------------------------------
/* // 如果编译器提示重复定义，请删除或注释以下两行
int OnInit() { return(INIT_SUCCEEDED); }
void OnTick() {}
*/
//+------------------------------------------------------------------+
