//+------------------------------------------------------------------+
//|                                                     SOLUSD_EA.mq4|
//|                        Generated by ChatGPT                      |
//|   Strategy based on MA(6), MA(2), CCI(20), and Stochastic(20,2,2)|
//+------------------------------------------------------------------+
#property strict
#include "../../Libraries/stdlib.mq4"
// Input parameters
input int     FastMAPeriod = 2;
input int     SlowMAPeriod = 6;
input int     CCIPeriod    = 20;
input int     KPeriod      = 20;
input int     DPeriod      = 2;
input int     Slowing      = 2;
input double  LotSize      = 0.1;
input int     Slippage     = 3;
input double  StopLoss     = 200;
input double  TakeProfit   = 400;

// Global variables
int           ticket;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   Print("SOLUSD Expert Advisor initialized.");
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   Print("SOLUSD Expert Advisor stopped.");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   // Only operate on new bar
   static datetime lastTime = 0;
   if (Time[0] == lastTime)
      return;
   lastTime = Time[0];

   // Indicators
   double maFastPrev = iMA(NULL, 0, FastMAPeriod, 0, MODE_EMA, PRICE_CLOSE, 1);
   double maSlowPrev = iMA(NULL, 0, SlowMAPeriod, 0, MODE_EMA, PRICE_CLOSE, 1);
   double maFastCurr = iMA(NULL, 0, FastMAPeriod, 0, MODE_EMA, PRICE_CLOSE, 0);
   double maSlowCurr = iMA(NULL, 0, SlowMAPeriod, 0, MODE_EMA, PRICE_CLOSE, 0);

   double cciCurr = iCCI(NULL, 0, CCIPeriod, PRICE_TYPICAL, 0);
   double cciPrev = iCCI(NULL, 0, CCIPeriod, PRICE_TYPICAL, 1);

   double kCurr = iStochastic(NULL, 0, KPeriod, DPeriod, Slowing, MODE_SMA, 0, MODE_MAIN, 0);
   double dCurr = iStochastic(NULL, 0, KPeriod, DPeriod, Slowing, MODE_SMA, 0, MODE_SIGNAL, 0);

   // Entry logic: Buy
   if (maFastPrev < maSlowPrev && maFastCurr > maSlowCurr && cciCurr > 100 && kCurr > dCurr && kCurr < 80)
   {
      if (OrdersTotal() == 0)
      {
         ticket = OrderSend(Symbol(), OP_BUY, LotSize, Ask, Slippage, Ask - StopLoss * Point, Ask + TakeProfit * Point, "Buy Order", 0, 0, clrGreen);
         if (ticket < 0)
            Print("Buy Order Failed: ", ErrorDescription(GetLastError()));
      }
   }

   // Entry logic: Sell
   if (maFastPrev > maSlowPrev && maFastCurr < maSlowCurr && cciCurr < -100 && kCurr < dCurr && kCurr > 20)
   {
      if (OrdersTotal() == 0)
      {
         ticket = OrderSend(Symbol(), OP_SELL, LotSize, Bid, Slippage, Bid + StopLoss * Point, Bid - TakeProfit * Point, "Sell Order", 0, 0, clrRed);
         if (ticket < 0)
            Print("Sell Order Failed: ", ErrorDescription(GetLastError()));
      }
   }
}

//+------------------------------------------------------------------+
