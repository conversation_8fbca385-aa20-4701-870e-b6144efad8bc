//+------------------------------------------------------------------+
//|                                                 CCI_ATR_EA.mq4 |
//|                        Copyright 2025, MetaQuotes Software Corp. |
//|                                              https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, MetaQuotes Software Corp."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property strict

//--- Input parameters
extern int    CCI_Period    = 14;      // CCI Period
extern int    CCI_Level_Up  = 150;     // CCI Upper Level for Buy Signal (Increased)
extern int    CCI_Level_Down= -150;    // CCI Lower Level for Sell Signal (Increased)
extern int    ATR_Period    = 14;      // ATR Period
extern double ATR_SL_Mult   = 2.0;     // ATR Multiplier for Stop Loss
extern double ATR_TP_Mult   = 4.0;     // ATR Multiplier for Take Profit (Increased)
extern double Lots          = 0.01;    // Lot Size
extern int    MagicNumber   = 12345;   // Magic Number for Orders
extern int    MA_Period     = 50;      // Moving Average Period for Trend Filter
//--- Trailing Stop Parameters
extern bool   EnableTrailingStop = true; // Enable Trailing Stop?
extern double TrailingStopATRMult= 1.5;  // ATR Multiplier for Trailing Stop distance

//--- Global variables
int Slippage = 3; // Slippage in points

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   //--- Check input parameters
   if(ATR_SL_Mult <= 0 || ATR_TP_Mult <= 0)
   {
      Print("Error: ATR Multipliers must be positive.");
      return(INIT_FAILED);
   }
   if(Lots <= 0)
   {
      Print("Error: Lot size must be positive.");
      return(INIT_FAILED);
   }
   //--- Check StopLevel
   if(MarketInfo(Symbol(), MODE_STOPLEVEL) * Point > ATR_SL_Mult * iATR(Symbol(), Period(), ATR_Period, 0))
   {
       Print("Stop Loss based on ATR (", ATR_SL_Mult * iATR(Symbol(), Period(), ATR_Period, 0), ") is smaller than the minimum StopLevel (", MarketInfo(Symbol(), MODE_STOPLEVEL) * Point, "). Increase ATR_SL_Mult or use a different SL logic.");
       // Optional: Adjust SL automatically or prevent initialization
       // return(INIT_FAILED);
   }

   //--- Initialization successful
   Print("CCI_ATR_EA initialized successfully.");
   return(INIT_SUCCEEDED);
}
//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   //---
   Print("CCI_ATR_EA deinitialized. Reason code: ", reason);
}
//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   //--- Check if trading is allowed
   if(!IsTradeAllowed() || IsStopped()) return;

   //--- Ensure we have enough bars for calculations
   if(Bars < CCI_Period + 2 || Bars < ATR_Period + 2 || Bars < MA_Period + 2)
   {
      Print("Not enough bars for indicator calculation.");
      return;
   }

   //--- Calculate indicator values
   double cci_current = iCCI(NULL, 0, CCI_Period, PRICE_TYPICAL, 0);
   double cci_previous = iCCI(NULL, 0, CCI_Period, PRICE_TYPICAL, 1);
   double atr_current = iATR(NULL, 0, ATR_Period, 0);
   double ma_current = iMA(NULL, 0, MA_Period, 0, MODE_SMA, PRICE_CLOSE, 0); // Simple Moving Average on Close

   //--- Check for valid ATR value
   if(atr_current <= 0)
   {
       Print("Invalid ATR value: ", atr_current);
       return; // Cannot calculate SL/TP without valid ATR
   }

   //--- Check for existing orders for this symbol and magic number
   int total_orders = 0;
   for(int i = OrdersTotal() - 1; i >= 0; i--)
   {
      if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
      {
         if(OrderSymbol() == Symbol() && OrderMagicNumber() == MagicNumber)
         {
            total_orders++;
         }
      }
   }

   //--- Trading Logic
   //--- No open orders, check for entry signals
   if(total_orders == 0)
   {
      //--- Calculate SL and TP distances in points
      double sl_distance_points = ATR_SL_Mult * atr_current / Point;
      double tp_distance_points = ATR_TP_Mult * atr_current / Point;

      //--- Ensure SL/TP distances meet minimum StopLevel requirements
      double min_stop_level_points = MarketInfo(Symbol(), MODE_STOPLEVEL);
      if(sl_distance_points < min_stop_level_points)
      {
          Print("Calculated SL distance (", sl_distance_points, " points) is less than minimum StopLevel (", min_stop_level_points, " points). Adjusting SL.");
          sl_distance_points = min_stop_level_points;
      }
       if(tp_distance_points < min_stop_level_points) // Also check TP distance
      {
          Print("Calculated TP distance (", tp_distance_points, " points) is less than minimum StopLevel (", min_stop_level_points, " points). Adjusting TP.");
          tp_distance_points = min_stop_level_points;
      }


      //--- Refresh rates before placing order
      RefreshRates();
      double current_ask = MarketInfo(Symbol(), MODE_ASK);
      double current_bid = MarketInfo(Symbol(), MODE_BID);

      //--- Buy Signal: CCI crosses above Upper Level AND Price is above MA
      if(cci_previous < CCI_Level_Up && cci_current >= CCI_Level_Up && current_ask > ma_current)
      {
         double stop_loss = NormalizeDouble(current_ask - sl_distance_points * Point, Digits);
         double take_profit = NormalizeDouble(current_ask + tp_distance_points * Point, Digits);
         int ticket = OrderSend(Symbol(), OP_BUY, Lots, current_ask, Slippage, stop_loss, take_profit, "CCI_ATR_Buy", MagicNumber, 0, clrBlue);
         if(ticket < 0)
         {
            Print("OrderSend (Buy) failed with error #", GetLastError());
         }
         else
         {
            Print("Buy order placed successfully. Ticket: ", ticket, " SL: ", stop_loss, " TP: ", take_profit);
         }
         return; // Exit after attempting order
      }

      //--- Sell Signal: CCI crosses below Lower Level AND Price is below MA
      if(cci_previous > CCI_Level_Down && cci_current <= CCI_Level_Down && current_bid < ma_current)
      {
         double stop_loss = NormalizeDouble(current_bid + sl_distance_points * Point, Digits);
         double take_profit = NormalizeDouble(current_bid - tp_distance_points * Point, Digits);
         int ticket = OrderSend(Symbol(), OP_SELL, Lots, current_bid, Slippage, stop_loss, take_profit, "CCI_ATR_Sell", MagicNumber, 0, clrRed);
         if(ticket < 0)
         {
            Print("OrderSend (Sell) failed with error #", GetLastError());
         }
         else
         {
            Print("Sell order placed successfully. Ticket: ", ticket, " SL: ", stop_loss, " TP: ", take_profit);
         }
         return; // Exit after attempting order
      }
   } // This brace closes the if(total_orders == 0) block
   //--- Manage existing orders (Trailing Stop)
   // Changed 'else if' to 'if' because the trailing stop should run if orders exist,
   // regardless of whether an entry signal was checked in this tick (due to returns in entry logic).
   if (EnableTrailingStop && total_orders > 0)
   {
      ManageTrailingStop(atr_current);
   }
}
//+------------------------------------------------------------------+
//| Trailing Stop Management Function                                |
//+------------------------------------------------------------------+
void ManageTrailingStop(double current_atr)
{
   if(current_atr <= 0 || TrailingStopATRMult <= 0) return; // Need valid ATR and multiplier

   double trailing_stop_distance_points = TrailingStopATRMult * current_atr / Point;
   double min_stop_level_points = MarketInfo(Symbol(), MODE_STOPLEVEL);

   // Ensure trailing stop distance is not smaller than minimum stop level
   if(trailing_stop_distance_points < min_stop_level_points)
   {
      trailing_stop_distance_points = min_stop_level_points;
   }

   for(int i = OrdersTotal() - 1; i >= 0; i--)
   {
      if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
      {
         // Check if it's our order
         if(OrderSymbol() == Symbol() && OrderMagicNumber() == MagicNumber)
         {
            //--- Refresh rates
            RefreshRates();
            double current_ask = MarketInfo(Symbol(), MODE_ASK);
            double current_bid = MarketInfo(Symbol(), MODE_BID);
            double new_stop_loss = 0;
            bool modify_order = false;

            //--- Trailing for BUY orders
            if(OrderType() == OP_BUY)
            {
               // Calculate potential new stop loss
               new_stop_loss = NormalizeDouble(current_bid - trailing_stop_distance_points * Point, Digits);
               // Check if the new SL is higher than the current SL AND higher than the open price (ensures profit lock)
               if(new_stop_loss > OrderStopLoss() && new_stop_loss > OrderOpenPrice())
               {
                  // Check if the new SL is valid (not too close to current price)
                  if(current_bid - new_stop_loss >= min_stop_level_points * Point)
                  {
                     modify_order = true;
                  }
                  else
                  {
                     // If too close, adjust slightly away from price if possible
                     new_stop_loss = NormalizeDouble(current_bid - min_stop_level_points * Point, Digits);
                     if(new_stop_loss > OrderStopLoss() && new_stop_loss > OrderOpenPrice())
                     {
                        modify_order = true;
                     }
                  }
               }
            }
            //--- Trailing for SELL orders
            else if(OrderType() == OP_SELL)
            {
               // Calculate potential new stop loss
               new_stop_loss = NormalizeDouble(current_ask + trailing_stop_distance_points * Point, Digits);
               // Check if the new SL is lower than the current SL AND lower than the open price (ensures profit lock)
               if(new_stop_loss < OrderStopLoss() && new_stop_loss < OrderOpenPrice())
               {
                  // Check if the new SL is valid (not too close to current price)
                  if(new_stop_loss - current_ask >= min_stop_level_points * Point)
                  {
                     modify_order = true;
                  }
                   else
                  {
                     // If too close, adjust slightly away from price if possible
                     new_stop_loss = NormalizeDouble(current_ask + min_stop_level_points * Point, Digits);
                     if(new_stop_loss < OrderStopLoss() && new_stop_loss < OrderOpenPrice())
                     {
                        modify_order = true;
                     }
                  }
               }
            }

            //--- Modify the order if needed
            if(modify_order)
            {
               bool result = OrderModify(OrderTicket(), OrderOpenPrice(), new_stop_loss, OrderTakeProfit(), 0, clrNONE);
               if(!result)
               {
                  Print("OrderModify (Trailing Stop) failed for ticket ", OrderTicket(), ". Error: ", GetLastError());
               }
               else
               {
                  Print("Trailing Stop updated for ticket ", OrderTicket(), " to ", new_stop_loss);
               }
            }
         }
      }
   }
}
//+------------------------------------------------------------------+
