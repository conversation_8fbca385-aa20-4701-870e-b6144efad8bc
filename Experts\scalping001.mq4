//+------------------------------------------------------------------+
//|                                                      Scalping.mq4|
//|                        Copyright 2023, MetaQuotes Software Corp. |
//|                                             http://www.metaquotes.net/ |
//+------------------------------------------------------------------+
#property strict

// Input parameters for indicators and trading settings
input int    MACD_FastEMA = 12;
input int    MACD_SlowEMA = 26;
input int    MACD_SignalSmoothing = 9;
input double RSI_Period = 14;
input double RSI_OverboughtLevel = 70;
input double RSI_OversoldLevel = 30;
input int    CCI_Period = 20;
input double CCI_OverboughtLevel = 100;
input double CCI_OversoldLevel = -100;
input int MAGIC_NUMBER= 888;

// Lot size for trading
input double LotSize = 0.1;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    //--- Initialization code
    return(INIT_SUCCEEDED);
}
//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    //--- Deinitialization code
}
//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    double macdMain, macdSignal;
    int rsiValue = iRSI(_Symbol, 0, (int)RSI_Period, PRICE_CLOSE, 0);
    double cciValue = iCCI(_Symbol, 0, CCI_Period, PRICE_CLOSE, 0);

    // Get MACD values
    if(MarketInfo(_Symbol, MODE_TICKSIZE)>0)
    {
        macdMain = iMACD(_Symbol, 0, MACD_FastEMA, MACD_SlowEMA, MACD_SignalSmoothing, PRICE_CLOSE, 0);
        macdSignal = iMACD(_Symbol, 0, MACD_FastEMA, MACD_SlowEMA, MACD_SignalSmoothing, PRICE_CLOSE, 1);
    }

    // Check for buy conditions
    if(rsiValue < RSI_OversoldLevel &&
       cciValue < CCI_OversoldLevel &&
       macdMain > macdSignal)
    {
        // Buy order logic
        OpenBuyOrder();
    }
    // Check for sell conditions
    else if (rsiValue > RSI_OverboughtLevel &&
             cciValue > CCI_OverboughtLevel &&
             macdMain < macdSignal)
    {
        // Sell order logic
        OpenSellOrder();
    }

    // Check for take profit and stop loss
    CheckOrders();
}

//+------------------------------------------------------------------+
//| Function to open buy orders                                      |
//+------------------------------------------------------------------+
void OpenBuyOrder()
{
    if(PositionsTotal() == 0 || (PositionSelect(_Symbol) && PositionGetInteger(POSITION_TYPE) != POSITION_TYPE_BUY))
    {
        double sl = Ask - 10 * Point;
        double tp = Ask + 20 * Point;

        int ticket = OrderSend(_Symbol, OP_BUY, LotSize, Ask, 3, sl, tp, "Buy Order", MAGIC_NUMBER, 0, clrGreen);
        if(ticket < 0)
        {
            Print("Error opening buy order: ", GetLastError());
        }
    }
}

//+------------------------------------------------------------------+
//| Function to open sell orders                                     |
//+------------------------------------------------------------------+
void OpenSellOrder()
{
    if(PositionsTotal() == 0 || (PositionSelect(_Symbol) && PositionGetInteger(POSITION_TYPE) != POSITION_TYPE_SELL))
    {
        double sl = Bid + 10 * Point;
        double tp = Bid - 20 * Point;

        int ticket = OrderSend(_Symbol, OP_SELL, LotSize, Bid, 3, sl, tp, "Sell Order", MAGIC_NUMBER, 0, clrRed);
        if(ticket < 0)
        {
            Print("Error opening sell order: ", GetLastError());
        }
    }
}

//+------------------------------------------------------------------+
//| Function to check and close orders                               |
//+------------------------------------------------------------------+
void CheckOrders()
{
    for(int i = 0; i < OrdersTotal(); i++)
    {
        if(OrderSelect(i, SELECT_BY_POS) && OrderType() == OP_BUY)
        {
            if(Bid - OrderOpenPrice() >= 20 * Point)
            {
                OrderClose(OrderTicket(), OrderLots(), Bid - 5 * Point, 3, clrGreen);
            }
            else if(OrderOpenPrice() - Bid >= 10 * Point)
            {
                OrderClose(OrderTicket(), OrderLots(), Bid + 5 * Point, 3, clrRed);
            }
        }
        else if (OrderSelect(i, SELECT_BY_POS) && OrderType() == OP_SELL)
        {
            if(OrderOpenPrice() - Ask >= 20 * Point)
            {
                OrderClose(OrderTicket(), OrderLots(), Ask + 5 * Point, 3, clrRed);
            }
            else if(Ask - OrderOpenPrice() >= 10 * Point)
            {
                OrderClose(OrderTicket(), OrderLots(), Ask - 5 * Point, 3, clrGreen);
            }
        }
    }
}
